<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="iaas, infrastructure as a service, active directory pricing" name="keywords"/>
    <meta content="Azure Active Directory B2C is an identity and access management cloud solution for your consumer-facing web and mobile apps. A 1RMB Trial gets you RMB1,500 in service credits."
          name="description"/>
    <title>
        Active Directory External Identities - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/active-directory-b2c/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-active-directory-b2c" wacn.date="11/27/2015">
                    </tags>
                    <style>
                        #aad-b2c-table-mau tr, #aad-b2c-table-separate tr {
                            background-color: rgb(255, 255, 255) !important;
                        }
                    </style>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/activity_directory_b2c.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/media/images/production/entra-identity.svg"/>
                                <h2>
                                    Azure Active Directory B2C
                                </h2>
                                <h4>
                                    Consumer identity and access management for the cloud
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure Active Directory B2C is a cloud-based identity and access management solution for your consumer-facing web and
                            mobile applications. It is a highly-available global service that scales to hundreds of millions of consumer
                            identities. Built on an enterprise-grade secure platform, Azure Active Directory B2C keeps your business and your
                            consumers protected.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <h2>
                            Pricing details
                        </h2>
                        <p>Azure AD B2C pricing is based on Monthly Active Users (MAUs), helping you to reduce costs and forecast with confidence. Benefit from a free tier and flexible, predictable pricing for external users:</p>
                        <ul>
                            <li>Free goes further: Your first 50,000 MAUs per month are free for both Premium P1 and Premium P2 features.</li>
                            <li>Flexible: Connect with customers and partners based on the usage and features you need, rather than the licenses you have.</li>
                            <li>Predictable: Pay only for what you use. Forecast with ease as your business and usage scales. Customers are not charged for a MAU’s subsequent authentications or for storing inactive users within that calendar month.</li>
                        </ul>
                        <p>Azure AD B2C is billed starting at the following rates, including for Enterprise Agreement customers. Premium P2 features include all the Premium P1 features and market-leading Identity Protection and Identity Governance controls, such as risk-based Conditional Access policies and Identity Protection reporting for Azure AD B2C.</p>
                        <div class="tags-date">
                            <div class="ms-date">
                                *The following prices are tax-inclusive.
                            </div>
                            <br/>
                            <div class="ms-date">
                                *Monthly price estimation is based on a usage of 31 days per month.
                            </div>
                        </div>
                        <table cellpadding="0" cellspacing="0" id="aad-b2c-table-mau">
                            <tr>
                                <th align="left" width="33%">
                                    <strong>
                                    </strong>
                                </th>
                                <th align="left" width="33%">
                                    <strong>
                                        Premium P1
                                    </strong>
                                </th>
                                <th align="left" width="33%">
                                    <strong>
                                        Premium P2
                                    </strong>
                                </th>
                            </tr>
                            <tr>
                                <td>
                                    First 50,000 MAU U
                                </td>
                                <td>
                                    ¥0/Monthly Active Users
                                </td>
                                <td>
                                    ¥0/Monthly Active Users
                                </td>
                                <td rowspan="5">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    More than 50,000 MAU
                                </td>
                                <td>
                                    ¥0.033/Monthly Active Users
                                </td>
                                <td>
                                    ¥0.165/Monthly Active Users
                                </td>
                            </tr>
                        </table>
                        <h4>
                            Separate Charges:
                        </h4>
                        <div class="tags-date">
                            <div class="ms-date">
                                *The following prices are tax-inclusive.
                            </div>
                            <br/>
                            <div class="ms-date">
                                *Monthly price estimation is based on a usage of 31 days per month.
                            </div>
                        </div>
                        <table cellpadding="0" cellspacing="0" id="aad-b2c-table-separate" width="100%">
                            <tr>
                                <th align="left">
                                    MFA and SMS/Phone-based Events
                                </th>
                                <th align="left">
                                    ¥ 0.305 per SMS/Phone event
                                </th>
                            </tr>
                        </table>
                        <p>
                            We listened carefully to customers about their needs regarding predictable, intuitive, and cost-efficient billing for
                            customer identity solutions. With MAU-based billing, customers benefit from:
                        </p>
                        <ul>
                            <li>
                                <b>
                                    Expanded Free tier:
                                </b>
                                The new Free tier stretches further by granting customers 50,000 MAUs per
                                month-enabling more authentications per unique user than under the previous free allotment of 50,000
                                authentications. Most customers-with 50,000 or fewer MAUs per month-will be able to use Azure AD B2C for free.
                            </li>
                            <li>
                                <b>
                                    Predictable forecasting:
                                </b>
                                Estimating monthly billing on a per MAU basis is simpler and more predictable
                                than trying to forecast the number of monthly authentications.
                            </li>
                            <li>
                                <b>
                                    Usage-based billing:
                                </b>
                                Pay only for your MAUs. Customers are not charged for a MAU`s subsequent
                                authentications or for storing inactive users within that calendar month.
                                <p>
                                    For example, if you have 100,000 monthly active users (MAUs) using standard features, your monthly bill would
                                    be ¥ 1,650 regardless of how many times each MAU authenticates:
                                </p>
                                <p>
                                    (50,000 MAUs x ¥0(Free tier)) + (50,000 MAUs x ¥0.033) = ¥1650
                                </p>
                                <p>
                                    A worldwide flat fee of ¥0.305 is billed for each MFA attempt or SMS/Phone event.
                                </p>
                            </li>
                        </ul>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="inentity-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            The free version of Azure Active Directory doesn’t offer a service level agreement. If you want to learn more about the
                            details of our server level agreement, please visit the
                            <a href="/en-us/support/sla/active-directory-b2c/" id="pricing_identity_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure Support Features:</p>
                         <p>We provide users with the following free support services:</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left"> </th>
                                 <th align="left"><strong>Supported or not</strong></th>
                             </tr>
                             <tr>
                                 <td>Billing and Subscription Management</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Service Dashboard</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web Event Submission</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Unlimited Disruption/Restoration</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Telephone Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP Filing Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>You can <a href="/en-us/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">request support online</a> or contact us via the service hotline.</p>
                         <h2>Service hotline:</h2>
                         <ul>
                             <li>************</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>Community help: <a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">Visit MSDN</a></p>
                         <p>For more support information, please visit <a href="/en-us/support/plans/" id="stor-sla-info">Azure Support Plans</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="4dzSq3xeuKWVc6uR1tjmsjezpcygfV-IQn46j_rGQEl_If8ZwfDEnbVbx_vRG0S7iniIgfOvwYUKMJKlvcbx4G5gXMUu-j0Fu5JDu_cgLiM1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
