<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure 公共 IP 地址, 价格, 常见问题" name="keywords"/>
  <meta content="了解 Azure IP Addresses价格详情。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   价格详情 - 公共 IP 地址 | Azure
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/ip-addresses/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <style>
        .ip_tit_font{
                    font-size:16px;
                }
       </style>
       <tags ms.date="09/30/2015" ms.service="ip-addresses" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/ip-addresses-slice-01.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/ip-addresses-icon.svg"/>
          <h2>
           公共 IP 地址
          </h2>
          <h4>
           用于标识给定虚拟机或云服务的动态地址或保留地址
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure 中公共 IP 地址的价格根据客户选择的 SKU（基础或标准）和 IP 地址类型（动态或静态）变动。下表总结了公共 IP 的定价结构。
        </p>
        <p>
         <em>
          <strong>
           注意
          </strong>
          ：Azure 有两种用于创建和处理资源的不同部署模型—
          <a href="https://go.microsoft.com/fwlink/?LinkId=397193&amp;clcid=0x804">
           资源管理器 (ARM) 和经典 (ASM)
          </a>
         </em>
         。
        </p>
        <h2>
         定价详细信息
        </h2>
        <!-- BEGIN: Table1-Content-->
        <!--<h3>保留的 IP 地址</h3>-->
        <div class="tags-date">
         <div class="ms-date">
          *以下价格均为含税价格。
         </div>
         <br/>
         <div class="ms-date">
          *每月价格估算基于每个月 744 小时的使用量。
         </div>
        </div>
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
          <th align="left">
           <strong>
            类型
           </strong>
          </th>
          <th align="left">
           <strong>
            基本（经典）
           </strong>
          </th>
          <th align="left">
           <strong>
            基本（ARM）
           </strong>
          </th>
          <th align="left">
           <strong>
            标准（ARM）
           </strong>
          </th>
         </tr>
         <tr>
          <td>
           动态 IP 地址
          </td>
          <td>
           第一个云服务 VIP：免费
           <br/>
           其他：¥0.026/小时（¥19.344/月）
           <sup>
            1
           </sup>
          </td>
          <td>
           ¥0.026/小时（¥19.344/月）
          </td>
          <td>
           <p>
            不适用
           </p>
          </td>
         </tr>
         <tr>
          <td>
           静态 IP 地址
          </td>
          <td>
           ¥0.026/小时
          </td>
          <td>
           ¥0.026/小时
          </td>
          <td>
           ¥0.026/小时
          </td>
         </tr>
         <tr>
          <td>
           <p>
            公共 IP 前缀
            <sup>
             2
            </sup>
           </p>
          </td>
          <td>
           <p>
            不适用
           </p>
          </td>
          <td>
           <p>
            不适用
           </p>
          </td>
          <td>
           <p>
            ¥0.03816/小时（¥28.391/月）
            <sup>
             3
            </sup>
           </p>
          </td>
         </tr>
        </table>
        <div class="tags-date">
         <div class="ms-date">
          <sup>
           1
          </sup>
          所有
          <a href="https://docs.azure.cn/zh-cn/virtual-network/virtual-networks-instance-level-public-ip">
           实例级别公共 IP 地址
          </a>
          (ILPIP) 都按 ￥ 0.026/小时 收费。每个云服务都可以获得一个
          <strong>
           免费
          </strong>
          <a href="https://docs.azure.cn/zh-cn/load-balancer/load-balancer-multivip">
           公共 VIP
          </a>
          。其他 VIP 按 ￥ 0.026/小时 收费。
         </div>
         <br/>
         <div class="ms-date">
          <sup>
           2
          </sup>
          公共 IP 前缀是一系列连续的公共 IP 地址。
         </div>
         <br/>
         <div class="ms-date">
          <sup>
           3
          </sup>
          公共 IP 前缀按每个 IP 每小时收费。创建前缀后，立即开始计费。
         </div>
         <br/>
         <div class="ms-date">
          IP 地址的限制在 Azure 中的全套
          <a href="https://docs.azure.cn/zh-cn/azure-subscription-service-limits">
           网络限制
          </a>
          中进行了详细说明。
          <a href="https://portal.azure.cn/">
           联系支持
          </a>
          ，根据业务需求增加默认限制（最多增加到最大限制）。
         </div>
        </div>
        <!-- END: Table1-Content-->
        <!-- BEGIN: Table2-Content-->
        <!-- END: Table2-Content-->
       </div>
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question6">
             我在 2017 年 9 月以前购买了公共 IP 地址。我有哪些 SKU？
            </a>
            <section>
             <p>
              如果你是在 2017 年 9 月之前购买的公共 IP 地址，则你使用的是基本公共 IP 地址。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question3">
             是否不足 1 小时按 1 小时计费？
            </a>
            <section>
             <p>
              是的。不足 1 小时按 1 小时计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question4">
             什么是未使用的保留 IP 地址？
            </a>
            <section>
             <p>
              这是未在正在运行的部署中使用的保留 IP 地址。我们象征性地对未使用的保留 IP 地址收取费用（参见本页定价表）。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question2">
             针对公共 IP 地址的计费时钟何时开始和停止？
            </a>
            <section>
             <p>
              对于 ARM 部署模型中的“静态”公共 IP 地址和 ASM 部署模型中的“保留” IP 地址，计费时钟将从您创建 IP 地址后的第 2 个小时开始，以便留出时间来相应分配 IP 地址。在您删除 IP 地址后计费时钟将停止。
             </p>
             <p>
              对于所有其他公共 IP 地址，时钟在启动相关资源时开始，在删除或停止解除分配相关资源时停止。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question7">
             在ARM模式下，虚拟机运行时的静态IP前五个是否收取保留费用？
            </a>
            <section>
             <p>
              收取使用费用，不收取保留费用
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question8">
             虚拟机处于“已停止解除分配”状态时，是否会向我收取公共 IP 地址的费用？
            </a>
            <section>
             <p>
              在 ARM 部署模型中，相关 VM 处于“已停止解除分配”时，不对“动态”公共 IP 地址收费。但是无论相关资源为何，都会向“静态”公共 IP 地址收费（除非它是该区域中前 5 个“静态”公共 IP 地址之一）。有关静态与动态分配方法的详细信息，请查看
              <a href="https://docs.azure.cn/virtual-network/virtual-network-ip-addresses-overview-arm/" id="public_IP_us2" style="color: #00a8d9;">
               Azure 中的 IP 地址
              </a>
              。
             </p>
             <p>
              在 ASM 部署模型中，VM 处于“已停止解除分配”时，不对实例层级公共 IP（ILPIP）地址收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question5">
             虚拟机处于已停止取消分配时，是否会对某个保留 IP 地址收费？
            </a>
            <section>
             <p>
              不，只要部署中有一个正在运行的虚拟机并且保留 IP 是包含的且在使用中的 5 个保留 IP 地址之一，便不予收费。
             </p>
             <p>
              但是，如果部署中的所有虚拟机都处于已停止取消分配状态，则将对保留 IP 地址收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="public_IP_question_price-for-specific-size-of-a-prefix">
             特定大小前缀的价格是多少？
            </a>
            <section>
             <p>
              下表显示了前缀的每小时费用，具体取决于其大小。
             </p>
             <table border="0" cellpadding="0" cellspacing="0" width="100%">
              <tr>
               <th align="left">
                <strong>
                 IP 地址块
                </strong>
               </th>
               <th align="left">
                <strong>
                 IP 编号
                </strong>
               </th>
               <th align="left">
                <strong>
                 每小时*
                </strong>
               </th>
              </tr>
              <tr>
               <td>
                /31
               </td>
               <td>
                2
               </td>
               <td>
                ￥ 0.07632
               </td>
              </tr>
              <tr>
               <td>
                /30
               </td>
               <td>
                4
               </td>
               <td>
                ￥ 0.15264
               </td>
              </tr>
              <tr>
               <td>
                /29
               </td>
               <td>
                8
               </td>
               <td>
                ￥ 0.30528
               </td>
              </tr>
              <tr>
               <td>
                /28
               </td>
               <td>
                16
               </td>
               <td>
                ￥ 0.61056
               </td>
              </tr>
              <tr>
               <td>
                /27
               </td>
               <td>
                32
               </td>
               <td>
                ￥ 1.22112
               </td>
              </tr>
              <tr>
               <td>
                /26
               </td>
               <td>
                64
               </td>
               <td>
                ￥ 2.44224
               </td>
              </tr>
              <tr>
               <td>
                /25
               </td>
               <td>
                128
               </td>
               <td>
                ￥ 4.88448
               </td>
              </tr>
              <tr>
               <td>
                /24
               </td>
               <td>
                256
               </td>
               <td>
                ￥ 9.76896
               </td>
              </tr>
             </table>
             <p>
              *每小时费用根据单个 IP 每小时价格 ￥ 0.038166 乘以块中的 IP 数得出。
             </p>
            </section>
           </div>
          </li>
          <!--
                        <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="public_IP_question6">保留 IP 和实例层级公共 IP 之间的差别是什么？</a>
                                <section>
                                    <p>一个部署中可能有多个虚拟机实例。 保留的IP是分配给整个部署的公共IP地址，可以被保留不变。实例层级公共 IP 是分配到一个虚拟机级别的公共IP地址，是临时的（非保留，会发生变化）。</p>
                                </section>
                            </div>
                        </li>
                        <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="public_IP_question7">我是否可以保留实例层级公共 IP 地址？</a>
                                <section>
                                    <p>不可以。目前，您只能保留 VIP。</p>
                                </section>
                            </div>
                        </li>
						-->
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="ip-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         IP 地址服务不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/legal/sla/index.html" id="pricing_ip-address_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--
-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="4f-BW-zc7fE1bJVytoSzs-61156wvEgoSXg-2ddoc1xkZGGZALhlxqxJM6J8t4UBD5abmgUvKdHDDW0Uk0936DFPW6RUVj7LchGfi4jWy8I1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
