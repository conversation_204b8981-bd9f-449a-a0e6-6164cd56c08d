<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="virtual wan, 虚拟广域网, 云服务提供商 Azure" name="keywords"/>
  <meta content="Azure Virtual WAN 是一种网络服务，它通过 Azure 提供经优化、自动的分支到分支连接性。" name="description"/>
  <title>
   定价 - Virtual WAN -  Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/virtual-wan/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="virtual-wan" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/virtual-wan-banner.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           虚拟 WAN
           <span>
            Virtual WAN
           </span>
          </h2>
          <h4>
           简单统一的全球连接与安全性
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure 虚拟 WAN 是一种网络服务，它通过 Azure 提供经优化、自动的分支到分支连接性。通过虚拟 WAN，客户可实现分支间的连接并将分支连接到 Azure，从而使用防火墙和 Azure 网络及安全服务等虚拟应用整合网络和安全需求。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <h2>
         定价详细信息
        </h2>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <div class="tags-date">
          <div class="ms-date">
           *以下价格均为含税价格。
          </div>
          <br/>
          <div class="ms-date">
           *每月价格估算基于每个月 744 小时的使用量。
          </div>
         </div>
         <table cellpadding="0" cellspacing="0" width="100%">
          <thead>
           <tr>
            <th align="left">
             <strong>
              类型
             </strong>
            </th>
            <th align="left">
             <strong>
              价格
             </strong>
            </th>
            <th align="left">
             <strong>
              单元
             </strong>
            </th>
           </tr>
          </thead>
          <tbody>
           <tr>
            <td class="left_align">
             标准虚拟 WAN 中心
            </td>
            <td class="left_align">
             ￥2.544/小时
            </td>
            <td class="left_align">
             1/部署小时
            </td>
           </tr>
           <tr>
            <td class="left_align">
             具有第三方集成的标准虚拟中心/安全虚拟中心
            </td>
            <td class="left_align">
             ￥4.068/小时
            </td>
            <td class="left_align">
             1/部署小时
            </td>
           </tr>
           <tr>
            <td class="left_align">
             标准虚拟 WAN 中心数据处理
            </td>
            <td class="left_align">
             ￥0.2034/GB
            </td>
            <td class="left_align">
             /GB
            </td>
           </tr>
           <tr>
            <td class="left_align">
             防火墙NVA数据处理
            </td>
            <td class="left_align">
             ￥0.05088/GB
            </td>
            <td class="left_align">
             /GB
            </td>
           </tr>
           <tr>
            <td class="left_align">
             VPN S2S 缩放单元
             <sup>
              1
             </sup>
            </td>
            <td class="left_align">
             ¥ 1.844/小时
            </td>
            <td class="left_align">
             每个缩放单元 500 Mbps
            </td>
           </tr>
           <tr>
            <td class="left_align">
             VPN S2S 连接单元
             <sup>
              2
             </sup>
            </td>
            <td class="left_align">
             ¥ 0.814/小时
            </td>
            <td class="left_align">
             1 个连接
            </td>
           </tr>
           <tr>
            <td class="left_align">
             VPN P2S 缩放单元
            </td>
            <td class="left_align">
             ￥3.674/小时
            </td>
            <td class="left_align">
             每个缩放单元 500 Mbps
            </td>
           </tr>
           <tr>
            <td class="left_align">
             VPN P2S 连接单元
            </td>
            <td class="left_align">
             ￥0.127/小时
            </td>
            <td class="left_align">
             1个连接
            </td>
           </tr>
           <tr>
            <td class="left_align">
             ExpressRoute 缩放单元
             <sup>
              3
             </sup>
            </td>
            <td class="left_align">
             ¥ 2.544/小时
            </td>
            <td class="left_align">
             每个缩放单元 2 Gbps
            </td>
           </tr>
           <tr>
            <td class="left_align">
             ExpressRoute 连接单元
            </td>
            <td class="left_align">
             ¥ 0.509/小时
            </td>
            <td class="left_align">
             1 个连接
            </td>
           </tr>
           <tr>
            <td class="left_align">
             NVA 基础结构单位
            </td>
            <td class="left_align">
             ¥ 2.544/小时
            </td>
            <td class="left_align">
             1 个单位
            </td>
           </tr>
           <tr>
            <td class="left_align">
             路由基础结构单位
            </td>
            <td class="left_align">
             ¥ 1.02/小时
            </td>
            <td class="left_align">
             1 个单位
            </td>
           </tr>
          </tbody>
         </table>
         <div class="tags-date">
          <div class="ms-date">
           <sup>
            1
           </sup>
           VPN S2S 缩放单元是所有分支站点与中心之间的整体连接速率，可提升到 20 Gbps。
          </div>
          <br/>
          <div class="ms-date">
           <sup>
            2
           </sup>
           VPN S2S 连接单位表示与中心连接的分支站点的数量，支持每个 Azure 区域最多 1,000 个连接。
          </div>
          <br/>
          <div class="ms-date">
           <sup>
            3
           </sup>
           缩放单元 6 到 10 按 ¥1.514/小时 的增量进行计费。
          </div>
         </div>
        </div>
        <h2>
         具有安全虚拟中心定价的 Azure 防火墙
        </h2>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <table cellpadding="0" cellspacing="0" width="100%">
          <thead>
           <tr>
            <th align="left">
             <strong>
              SKU
             </strong>
            </th>
            <th align="left">
             <strong>
              类型
             </strong>
            </th>
            <th align="left">
             <strong>
              价格
             </strong>
            </th>
            <th align="left">
             <strong>
              单位
             </strong>
            </th>
           </tr>
          </thead>
          <tbody>
           <tr>
            <td class="left_align" rowspan="2">
             标准
            </td>
            <td class="left_align">
             安全虚拟中心部署单元
            </td>
            <td class="left_align">
             ￥12.72/小时
            </td>
            <td class="left_align">
             1/部署小时
            </td>
           </tr>
           <tr>
            <td class="left_align">
             安全虚拟中心已处理的数据单元
            </td>
            <td class="left_align">
             ￥0.1632/GB
            </td>
            <td class="left_align">
             1/已处理GB
            </td>
           </tr>
           <tr>
            <td class="left_align" rowspan="2">
             高级
            </td>
            <td class="left_align">
             安全虚拟中心部署单元
            </td>
            <td class="left_align">
             ￥17.808/小时
            </td>
            <td class="left_align">
             1/部署小时
            </td>
           </tr>
           <tr>
            <td class="left_align">
             安全虚拟中心已处理的数据单元
            </td>
            <td class="left_align">
             ￥0.1632/GB
            </td>
            <td class="left_align">
             1/已处理GB
            </td>
           </tr>
          </tbody>
         </table>
         <div class="tags-date">
          <div class="ms-date">
           <p>
            有关Azure防火墙定价的更多详细信息，请参阅
            <a href="https://www.azure.cn/pricing/details/azure-firewall/">
             此处。
            </a>
           </p>
          </div>
          <br/>
         </div>
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="dns-que-q1">
             是否不足 1 小时按 1 小时计费？
            </a>
            <section>
             <p>
              相同。不足 1 小时按 1 小时计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="dns-que-q2">
             VPN 连接上的数据传输是否单独收费？
            </a>
            <section>
             <p>
              与本地站点或普通 Internet 站点之间通过 VPN 连接进行的数据传输按正常的数据传输费率单独收费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a aria-label="vpn-contact-page" href="https://support.azure.cn/zh-cn/support/contact" id="vpn-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证每个 VPN 或 ExpressRoute 的基本网关均具有 99.9% 的可用性。 我们保证每个 VPN 或 ExpressRoute 的标准网关均具有 99.95% 的可用性。 我们保证每个 VPN 或 ExpressRoute 的高性能网关均具有 99.95% 的可用性。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a aria-label="vpn_authentication_sla" href="../../../support/sla/vpn-gateway/index.html" id="vpn_authentication_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="3uUVzDEHX0jq_JZ5kWa4PUDfC2aLtd2sQ23veJtW6RN4n_veT1vQA6NKgOFIxCh7yWtQmWqvcGX6xsM3B2s7j3LczLHHRP19iUHeHWnfWpo1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
