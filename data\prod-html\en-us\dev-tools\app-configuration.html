<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="Get detailed pricing for App Configuration—fast, scalable parameter storage for app configuration. No upfront cost. No termination fees. Pay only for what you use."
          name="description"/>
    <title>
        App Configuration pricing
    </title>
    <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "en-us";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                loading...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="08/26/2020" ms.service="en-us-app-configuration" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .svg {
                            width: 50px;
                            float: left;
                            margin-right: 10px;
                        }

                        .link-a {
                                display: inline !important;
                                padding: 0;
                                background-color: transparent !important;
                                float: none !important;
                                color: #006FD4 !important;
                            }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <div class="svg">
                                    <svg aria-hidden="true" data-slug-id="app-configuration" role="presentation" viewbox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                                        <defs>
                                            <lineargradient gradientunits="userSpaceOnUse" id="app-configuration:bbaa244d-7a73-4def-ae33-b68783fa5b51-f3379804" x1="9"
                                                            x2="9" y1="15.63" y2="-2.51">
                                                <stop offset="0" stop-color="#0078d4">
                                                </stop>
                                                <stop offset="0.16" stop-color="#1380da">
                                                </stop>
                                                <stop offset="0.53" stop-color="#3c91e5">
                                                </stop>
                                                <stop offset="0.82" stop-color="#559cec">
                                                </stop>
                                                <stop offset="1" stop-color="#5ea0ef">
                                                </stop>
                                            </lineargradient>
                                            <lineargradient gradientunits="userSpaceOnUse" id="app-configuration:fa74d148-d6d0-4c75-94f7-94732b8c62b0-3465b0ec" x1="12.26"
                                                            x2="12.26" y1="7.17" y2="17.21">
                                                <stop offset="0" stop-color="#ffd70f">
                                                </stop>
                                                <stop offset="0.27" stop-color="#ffd310">
                                                </stop>
                                                <stop offset="0.54" stop-color="#ffc613">
                                                </stop>
                                                <stop offset="0.83" stop-color="#feb217">
                                                </stop>
                                                <stop offset="1" stop-color="#fea11b">
                                                </stop>
                                            </lineargradient>
                                        </defs>
                                        <path d="M17.41,8.4a3.77,3.77,0,0,0-3.28-3.63A4.76,4.76,0,0,0,9.22.21,4.92,4.92,0,0,0,4.53,3.4,4.48,4.48,0,0,0,.59,7.73a4.58,4.58,0,0,0,4.74,4.4,2.75,2.75,0,0,0,.41,0h7.67a.64.64,0,0,0,.2,0A3.82,3.82,0,0,0,17.41,8.4Z"
                                              fill="url(#app-configuration:bbaa244d-7a73-4def-ae33-b68783fa5b51-f3379804)">
                                        </path>
                                        <path d="M8.14,16v-.6l0,0-.61-.21-.16-.41.31-.62,0-.07-.19-.19-.23-.23-.08,0-.6.3-.41-.11-.26-.67H5.32l0,0-.2.61L4.67,14,4,13.62,3.56,14l0,.08.3.59-.17.41L3,15.38V16l.09,0,.63.21.17.41-.32.69.42.42.08,0,.6-.3.41.17.26.72h.59l0-.09.21-.63.4-.17.7.32.42-.42,0-.08-.3-.59.11-.42Zm-2.51.55a.84.84,0,1,1,.83-.84h0A.84.84,0,0,1,5.63,16.51Z"
                                              fill="#76bc2d">
                                        </path>
                                        <path d="M17.28,12.64V11.47l-.06-.05L16,11l-.31-.8L16.31,9l.06-.13L16,8.47,15.56,8l-.16.08-1.17.6-.8-.23-.51-1.3H11.76l-.06.06-.4,1.19-.82.31L9.13,8.07l-.82.82.08.16L9,10.21l-.33.8-1.41.51v1.17l.17.05,1.24.41L9,14l-.64,1.35.82.83.16-.07,1.17-.6.8.33.51,1.41H13L13,17l.41-1.24.79-.33,1.37.63.82-.82-.08-.16L15.74,14l.23-.82Zm-4.91,1.08A1.64,1.64,0,1,1,14,12.07h0A1.65,1.65,0,0,1,12.37,13.72Z"
                                              fill="url(#app-configuration:fa74d148-d6d0-4c75-94f7-94732b8c62b0-3465b0ec)">
                                        </path>
                                        <path d="M12.37,13.72A1.64,1.64,0,1,1,14,12.07h0A1.65,1.65,0,0,1,12.37,13.72Z" fill="#fff">
                                        </path>
                                    </svg>
                                </div>
                                <h2>
                                    App Configuration pricing
                                </h2>
                                <h4>
                                    Fast, scalable parameter storage for app configuration
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="pricing-page-section">
                            <p>
                                Get hosted, universal storage for all of your Azure apps. Manage configurations effectively and reliably, in real time, without affecting
                                customers by avoiding time-consuming redeployments. Azure App Configuration is built for speed, scalability, and security.
                            </p>
                        </div>
                        <div class="pricing-page-section">
                            <h2>
                                Pricing details
                            </h2>
                            <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                                <div class="tab-container-container">
                                    <div class="tab-container-box">
                                        <div class="tab-container">
                                            <div class="dropdown-container software-kind-container" style="display:none;">
                                                <label>
                                                    OS/Software:
                                                </label>
                                                <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
               <span class="selected-item">
                App Configuration
               </span>
                                                    <i class="icon">
                                                    </i>
                                                    <ol class="tab-items">
                                                        <li class="active">
                                                            <a data-href="#tabContent1" href="javascript:void(0)" id="home_App-Configuration">
                                                                App Configuration
                                                            </a>
                                                        </li>
                                                    </ol>
                                                </div>
                                                <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                                    <option data-href="#tabContent1" selected="selected" value="App Configuration">
                                                        App Configuration
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="dropdown-container region-container">
                                                <label>
                                                    Region:
                                                </label>
                                                <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                China North 3
                                                </span>
                                                    <i class="icon">
                                                    </i>
                                                    <ol class="tab-items">
                                                        <!-- id 对应 soft-category 的 region -->
                                                        <li class="active">
                                                            <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                                China North 3
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                                China East 2
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                                China North 2
                                                            </a>
                                                        </li>
<!--                                                         <li>
                                                            <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                                China East
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                                China North
                                                            </a>
                                                        </li> -->
                                                    </ol>
                                                </div>
                                                <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                                    <option data-href="#north-china3" value="north-china3">
                                                        China North 3
                                                    </option>
                                                    <option data-href="#east-china2" selected="selected" value="east-china2">
                                                        China East 2
                                                    </option>
                                                    <option data-href="#north-china2" value="north-china2">
                                                        China North 2
                                                    </option>
<!--                                                     <option data-href="#east-china" value="east-china">
                                                        China East
                                                    </option>
                                                    <option data-href="#north-china" value="north-china">
                                                        China North
                                                    </option> -->
                                                </select>
                                            </div>
                                            <div class="clearfix">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- BEGIN: TAB-CONTAINER-1 -->
                                <div class="tab-content">
                                    <!-- BEGIN: TAB-CONTENT-1 -->
                                    <div class="tab-panel" id="tabContent1">
                                        <!-- BEGIN: Tab level 2 navigator 2 -->
                                        <!-- BEGIN: Tab level 2 content 3 -->
                                        <ul class="tab-nav" style="display:none">
                                            <li class="active">
                                                <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="gpv1">
                                                    常规用途 v1
                                                </a>
                                            </li>
                                        </ul>
                                        <div class="tab-content">
                                            <!-- BEGIN: Table1-Content-->
                                            <div class="tab-panel" id="tabContent2">
                                                <!-- <div class="scroll-table" style="display: block;"></div> -->
                                                <h4>
                                                    Features and Quotas
                                                </h4>
                                                <table cellpadding="0" cellspacing="0" id="active-directory-standard-app-configuration" width="100%">
                                                    <thead>
                                                    <tr>
                                                        <th align="left" style="width: 300px;">
                                                        </th>
                                                        <th align="left">
                                                            FREE
                                                        </th>
                                                        <th align="left">
                                                            DEVELOPER
                                                        </th>
                                                        <th align="left">
                                                            STANDARD
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td>
                                                            Purpose
                                                        </td>
                                                        <td>
                                                            For evaluation and trial use
                                                        </td>
                                                        <td>
                                                            For low-volume development and testing use cases
                                                        </td>
                                                        <td>
                                                            For medium-volume use cases
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Resources per subscription
                                                            <br>
                                                            (A resource consists of a single configuration store)
                                                        </td>
                                                        <td>
                                                           3 per region
                                                        </td>
                                                        <td>
                                                            Unlimited
                                                        </td>
                                                        <td>
                                                            Unlimited
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Storage per resource
                                                        </td>
                                                        <td>
                                                            10 MB
                                                        </td>
                                                        <td>
                                                            500 MB
                                                        </td>
                                                        <td>
                                                            1 GB
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Revision history
                                                        </td>
                                                        <td>
                                                            7 days
                                                        </td>
                                                        <td>
                                                            7 days
                                                        </td>
                                                        <td>
                                                            30 days
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Throughput
                                                        </td>
                                                        <td>
                                                            No guaranteed throughput.
                                                        </td>
                                                        <td>
                                                            No guaranteed throughput.
                                                        </td>
                                                        <td>
                                                            Allow up to 300 requests per second (RPS) for read requests and up to 60 RPS for write requests.
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Request quota per resource
                                                        </td>
                                                        <td>
                                                            1,000 per day
                                                            <br/>
                                                            (Once the quota is exhausted, HTTP status code 429 will be returned for all requests until the end of the day)
                                                        </td>
                                                        <td>
                                                            6,000 per hour
                                                            <br/>
                                                            (Once the quota is exhausted, requests may return HTTP status code 429 indicating Too Many Requests - until the end of the hour)
                                                        </td>
                                                        <td>
                                                            30,000 per hour
                                                            <br/>
                                                            (Once the quota is exhausted, requests may return HTTP status code 429 indicating Too Many Requests - until the end of the hour). 
                                                            For Geo Replication enabled resources, 30,000 per hour per replica.
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            SLA
                                                        </td>
                                                        <td>
                                                            None
                                                        </td>
                                                        <td>
                                                            None
                                                        </td>
                                                        <td>
                                                            99.95%**
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Feature
                                                        </td>
                                                        <td>
                                                            Encryption with Microsoft-managed keys
                                                            <br/>
                                                            HMAC or AAD authentication
                                                            <br/>
                                                            RBAC support
                                                            <br/>
                                                            Managed identity
                                                            <br/>
                                                            Service tags
                                                        </td>
                                                        <td>
                                                            All Free tier functionality plus:
                                                            <br/>
                                                            Private Link support
                                                        </td>
                                                        <td>
                                                            All Standard tier functionality plus: 
                                                            <br/>
                                                            1 replica included (optional to configure during Store creation)
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Snapshot storage per resource*
                                                        </td>
                                                        <td>
                                                            10 MB
                                                        </td>
                                                        <td>
                                                            500 MB
                                                        </td>
                                                        <td>
                                                            1 GB
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Maximum Private Endpoints per resource
                                                        </td>
                                                        <td>
                                                            None
                                                        </td>
                                                        <td>
                                                            1
                                                        </td>
                                                        <td>
                                                            10
                                                        </td>
                                                    </tr>
                                                    <!--
                                                                                                <tr>
                                                                                                    <td>Cost</td>
                                                                                                    <td>Free</td>
                                                                                                    <td>￥12.21 per day, plus an overage charge at ￥0.612 per 10,000 requests <br>The first 200,000 requests are included in the daily charge. Additional requests will be billed as overage.</td>
                                                                                                </tr>
                                                                                                 -->
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        <sup>
                                                            *
                                                        </sup>
                                                        Snapshot storage quota is additional and not counted towards Storage per resource.
                                                        <br>
                                                        <sup>
                                                            **
                                                        </sup>
                                                        This SLA is only applicable when the store has at least one replica. If no replica is configured, the SLA will be 99.9%.
                                                    </div>
                                                </div>
                                            
                                                <!-- <div class="scroll-table" style="display: block;"></div> -->
                                                <h4>
                                                    Pricing Information
                                                </h4>
                                                <table cellpadding="0" cellspacing="0" id="pricing-information-standard-app-configuration" width="100%">
                                                    <thead>
                                                    <tr>
                                                        <th align="left" style="width: 300px;">
                                                        </th>
                                                        <th align="left" style="width: 250px;">
                                                            Free
                                                        </th>
                                                        <th align="left">
                                                            Developer
                                                        </th>
                                                        <th align="left">
                                                            Standard
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td>
                                                            Cost per store
                                                            <sup>
                                                                1
                                                            </sup>
                                                        </td>
                                                        <td>
                                                            Free
                                                        </td>
                                                        <td>
                                                            ￥12.21 per store per day, plus an overage charge at ￥4.32 per 10,000 Requests
                                                            <sup>
                                                            2
                                                            </sup>. 
                                                            The first 3,000 requests are included in the daily charge. Additional requests will be billed as overage.
                                                        </td>
                                                        <td>
                                                            ￥12.21 per store per day, plus an overage charge at ￥0.65 per 10,000 requests. 
                                                            The first 200,000 requests are included in the daily charge. Additional requests will be billed as overage.
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Cost per replica
                                                        </td>
                                                        <td>
                                                            Not applicable
                                                        </td>
                                                        <td>
                                                            Not applicable
                                                        </td>
                                                        <td>
                                                            ￥12.21 per replica per day plus an overage charge at ￥0.65 per 10,000 requests per replica. 
                                                            The first 200,000 requests for each replica are included in the daily charge. Additional requests will be billed as overage.
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        <sup>
                                                            1
                                                        </sup>
                                                        Cost per store doesn’t include the price of replicas. For replicas pricing, check "Cost per replica".
                                                        <br>
                                                        <sup>
                                                            2
                                                        </sup>
                                                        Prorated at 1,000
                                                        <br>
                                                        Have questions about App Configuration? Check out our<a class="link-a" style="font-size: 12px;"
                                                            href="https://docs.azure.cn/zh-cn/azure-app-configuration/faq"
                                                            aria-label="aLabel">FAQ</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need any help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" style="margin: 0px;">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support. To learn more about the details of our Service Level Agreement,
                            please visit the
                            <a href="../../../support/sla/app-configuration/" style="margin: 0px;">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token =
            '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
            .toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="../../../../Static/Scripts/require.js" type="text/javascript">
</script>
<script src="../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
