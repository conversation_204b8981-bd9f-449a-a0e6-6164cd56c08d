<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
</head>
<body>
<p>
    `
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Microsoft Cloud, Internet of Things, Azure IoT Edge, monitoring Internet of Things assets, two-way communications, and price" name="keywords"/>
    <meta content="Learn about the pricing details of Azure IoT Edge. deploy cloud services to IoT devices. No upfront costs. Pay as you go. FREE trial. A 1RMB Trial gets you ￥ 1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer to enjoy a Service Level Agreement of up to 99.99%."
          name="description"/>
</p>
<title>
    Azure IoT Edge Pricing – Azure Cloud Computing
</title>
<link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
<link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
<link href="/Static/Favicon/manifest.json" rel="manifest"/>
<link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
<meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
<meta content="#ffffff" name="theme-color"/>
<link href="https://azure.microsoft.com/pricing/details/iot-edge" rel="canonical"/>
<!-- BEGIN: Azure UI Style -->
<link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
<link href="/Static/CSS/common.min.css" rel="stylesheet"/>
<!-- END: Azure UI Style -->
<!-- BEGIN: Page Style -->
<!-- END: Page Style -->
<!-- BEGIN: Minified Page Style -->
<link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
<!-- END: Minified Page Style -->
<link href="/StaticService/css/service.min.css" rel="stylesheet"/>
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-iot-edge" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/azure-iot-edge.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/media/images/production/azure-iot-edge80.svg"/>
                                <h2>
                                    Azure IoT Edge
                                </h2>
                                <h4>
                                    Extend cloud intelligence and analytics to edge devices
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure IoT Edge deploys cloud services to IoT devices, allowing you to process data, run analytics, and gain insights
                            locally on your edge devices. This is made possible by deploying cloud services such as Artificial Intelligence,
                            Azure Functions and Azure Stream Analytics and managing edge devices from Azure IoT Hub. You can also deploy custom
                            code allowing you to deploy customized complex business logic on edge devices. Azure IoT Edge enables code symmetry
                            between the cloud and the edge, simplifying development efforts and allowing developers to focus on building
                            features that deliver end customer value instead of managing infrastructure.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <h2>
                                Pricing Details
                            </h2>
                            <p>
                                Azure IoT Hub is required for the secure management of devices and services deployed to the edge via Azure IoT Edge.
                                Azure IoT Edge is designed to run on both Windows and Linux, on hardware of your choosing. You would deploy Azure
                                IoT Edge on an IoT device with sufficient memory and other resources, typically a gateway class device.
                            </p>
                            <p>
                                Azure IoT Hub
                                <a href="/en-us/home/<USER>/iot-hub/">
                                    More information
                                </a>
                                <a href="/en-us/pricing/details/iot-hub/">
                                    Pricing
                                </a>
                            </p>
                            <p>
                                There is no charge for using Azure IoT Edge. Azure IoT Edge allows you to run multiple Azure services on the edge.
                                These Azure services running on IoT Edge will be billed according to their specific pricing.
                            </p>
                            <p>
                                For more information and pricing details on services that can be run on Azure IoT Edge, please visit the following
                                pages:
                            </p>
                            <p>
                                Azure Functions
                                <a href="/en-us/home/<USER>/azure-functions/">
                                    More information
                                </a>
                                <a href="/en-us/pricing/details/azure-functions/">
                                    Pricing
                                </a>
                            </p>
                            <p>
                                Azure Stream Analytics
                                <a href="/en-us/home/<USER>/stream-analytics/">
                                    More information
                                </a>
                                <a href="/en-us/pricing/details/stream-analytics/">
                                    Pricing
                                </a>
                            </p>
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="ih-que-q1">
                                            How is usage of IoT Edge billed?
                                        </a>
                                        <section>
                                            <p>
                                                Azure IoT Edge service itself is free but requires Azure IoT Hub for the secure management of devices and edge
                                                deployment. If you choose to access Azure services with IoT Edge you are billed for the specific service based
                                                on its billing model for use on the edge. For instance, if you choose to run Azure Stream Analytics on IoT Edge
                                                you will be billed for IoT Hub usage AND according to the Azure Stream Analytics on edge pricing.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="iot-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            Service Level Agreement is not available in the free edition of IoT Edge.
                        </p>
                        <p>
                            To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/legal/sla/" id="pricing_iot-edge_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure Support Features:</p>
                         <p>We provide users with the following free support services:</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left"> </th>
                                 <th align="left"><strong>Supported or not</strong></th>
                             </tr>
                             <tr>
                                 <td>Billing and Subscription Management</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Service Dashboard</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web Event Submission</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Unlimited Disruption/Restoration</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Telephone Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP Filing Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>You can <a href="/en-us/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">request support online</a> or contact us via the service hotline.</p>
                         <h2>Service hotline:</h2>
                         <ul>
                             <li>************</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>Community help: <a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">Visit MSDN</a></p>
                         <p>For more support information, please visit <a href="/en-us/support/plans/" id="stor-sla-info">Azure Support Plans</a></p>

                     -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="yHclh7aImUZ2-9giTkarEvN9TUI00BkCz_U5rxJ9tmwbd18cf_2yuTCJLubZKIK-zWN8Pc9rGQYlWa3bIUWY8iFFJKeN7ktzNpxOdyWxvyM1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
