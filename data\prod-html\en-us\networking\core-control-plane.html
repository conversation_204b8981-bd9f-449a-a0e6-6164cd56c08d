<html lang="en-us" class="desktop"><head>
    <meta charset="utf-8">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta content="" name="description">
    <title>Azure Arc</title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180">
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon">
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon">
    <link href="/Static/Favicon/manifest.json" rel="manifest">
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon">
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config">
    <meta content="#ffffff" name="theme-color">
    <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical">
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="/Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet">
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet">
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "en-us";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm"><h3>Azure Stack HCI</h3><ul class="accordian-menu"><li><button id="leftnav_product_storage_details" class="menu-title expand" role="button" aria-expanded="true" title="Product"><span class="nav-title">Product</span><span class="icon icon-arrow-top"></span></button><ul class="menu-child accordian-menu" style="display: block;"><li><a href="/en-us/products/azure-stack/hci/" id="leftnav_product_storage-hci_function_Product overview" title="Product overview">Product overview</a></li><li><a href="/en-us/pricing/details/azure-stack/hci/" id="leftnav_product_storage-tables_pricing_hci_Pricing details" title="Pricing details">Pricing details</a></li><li><a href="/pricing/calculator/" id="leftnav_pricing_calculator_Pricing calculator" title="Pricing calculator">Pricing calculator</a></li><li><a href="https://docs.microsoft.com/en-us/azure-stack/hci/" id="leftnav_product_storage_documentation_Documentation" title="Documentation" target="_blank">Documentation</a></li></ul></li><li><button id="leftnav_product_storage_related" class="menu-title" role="button" aria-expanded="true" title="Related products"><span class="nav-title">Related products</span><span class="icon icon-arrow-top"></span></button><ul class="menu-child accordian-menu" style=""><li><a href="https://azure.microsoft.com/en-us/services/sql-database/" id="leftnav_product_sql-database_function_SQL Database" title="SQL Database" target="_blank">SQL Database</a></li><li><a href="https://azure.microsoft.com/en-us/services/virtual-machines/" id="leftnav_product_virtual-machines_function_Virtual Machines" title="Virtual Machines" target="_blank">Virtual Machines</a></li><li><a href="https://azure.microsoft.com/en-us/services/virtual-network/" id="leftnav_product_networking_function_Virtual Network" title="Virtual Network" target="_blank">Virtual Network</a></li></ul></li><li><button id="leftnav_product_storage_others" class="menu-title" role="button" aria-expanded="true" title="Related documentation and links"><span class="nav-title">Related documentation and links</span><span class="icon icon-arrow-top"></span></button><ul class="menu-child accordian-menu" style=""><li><a href="https://docs.azure.cn/en-us/azure-stack/hci/overview" id="leftnav_product_storage_solutions_web_What is Azure Stack HCI?" title="What is Azure Stack HCI?" target="_blank">What is Azure Stack HCI?</a></li><li><a href="https://docs.azure.cn/en-us/azure-stack/hci/get-started" id="leftnav_product_storage_solutions_mobile-services_Use Azure Stack HCI with Windows Admin Center" title="Use Azure Stack HCI with Windows Admin Center" target="_blank">Use Azure Stack HCI with Windows Admin Center</a></li><li><a href="https://docs.azure.cn/en-us/azure-stack/hci/deploy/deployment-quickstart" id="leftnav_product_storage_solutions_virtual-machines_Create an Azure Stack HCI cluster" title="Create an Azure Stack HCI cluster" target="_blank">Create an Azure Stack HCI cluster</a></li><li><a href="https://docs.azure.cn/en-us/azure-stack/hci/deploy/operating-system" id="leftnav_product_storage_solutions_virtual-machines_Deploy the Azure Stack HCI OS" title="Deploy the Azure Stack HCI OS" target="_blank">Deploy the Azure Stack HCI OS</a></li></ul></li></ul></div>
                </div>
                <div class="pure-content col-md-10">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select aria-label="leftNavSelect">
                            
                        <optgroup label="Product"><option id="leftnav_product_storage-hci_function_Product overview_mobile" value="/en-us/products/azure-stack/hci/">Product overview</option><option id="leftnav_product_storage-tables_pricing_hci_Pricing details_mobile" value="/en-us/pricing/details/azure-stack/hci/">Pricing details</option><option id="leftnav_pricing_calculator_Pricing calculator_mobile" value="/pricing/calculator/" disabled="" class="del-xs">Pricing calculator</option><option id="leftnav_product_storage_documentation_Documentation_mobile" value="https://docs.microsoft.com/en-us/azure-stack/hci/">Documentation</option></optgroup><optgroup label="Related products"><option id="leftnav_product_sql-database_function_SQL Database_mobile" value="https://azure.microsoft.com/en-us/services/sql-database/">SQL Database</option><option id="leftnav_product_virtual-machines_function_Virtual Machines_mobile" value="https://azure.microsoft.com/en-us/services/virtual-machines/">Virtual Machines</option><option id="leftnav_product_networking_function_Virtual Network_mobile" value="https://azure.microsoft.com/en-us/services/virtual-network/">Virtual Network</option></optgroup><optgroup label="Related documentation and links"><option id="leftnav_product_storage_solutions_web_What is Azure Stack HCI?_mobile" value="https://docs.azure.cn/en-us/azure-stack/hci/overview">What is Azure Stack HCI?</option><option id="leftnav_product_storage_solutions_mobile-services_Use Azure Stack HCI with Windows Admin Center_mobile" value="https://docs.azure.cn/en-us/azure-stack/hci/get-started">Use Azure Stack HCI with Windows Admin Center</option><option id="leftnav_product_storage_solutions_virtual-machines_Create an Azure Stack HCI cluster_mobile" value="https://docs.azure.cn/en-us/azure-stack/hci/deploy/deployment-quickstart">Create an Azure Stack HCI cluster</option><option id="leftnav_product_storage_solutions_virtual-machines_Deploy the Azure Stack HCI OS_mobile" value="https://docs.azure.cn/en-us/azure-stack/hci/deploy/operating-system">Deploy the Azure Stack HCI OS</option></optgroup></select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-core-control" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .svg {
                            width: 50px;
                            float: left;
                            margin-right: 10px;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}" style="background-color: rgb(227, 244, 255);">
                        <div class="common-banner-image" style="background-image: url(&quot;&quot;);">
                            <div class="common-banner-title">
                                <div class="svg">
                                    <img src="/Static/Images/SVG/00756-icon-service-Azure-Arc.svg" alt="">
                                </div>
                                <h2>
                                    Azure Arc
                                </h2>
                                <h4>
                                    Azure Arc is a bridge that extends the Azure platform to help you build applications and services with the flexibility to run across datacenters, at the edge, and in multi-cloud environments.
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>Enabled Kubernetes</h2>
                        <p>
                            Azure Arc enabled Kubernetes enables customers to connect and configure Kubernetes clusters across customer datacenters, edge locations, and multi-cloud.
                        </p>
                        <p>
                            Any number of Kubernetes clusters can be connected and organized in the Azure Portal at no additional cost. Like Arc enabled servers, add-on Azure management services, like Kubernetes Configuration, are charged when enabled.
                        </p>
                        
                        <h2>Kubernetes Configuration</h2>

                        <p>Kubernetes Configuration delivers configuration management and application deployment using GitOps. With this capability, cluster admins can declare their cluster configuration and applications in Git. The development teams can then use pull requests and the tools they are familiar with (existing DevOps pipelines, Git, Kubernetes manifests, Helm charts) to easily deploy applications onto Azure Arc enabled Kubernetes clusters and make updates in production. The GitOps agents listen to changes and facilitate automated rollbacks if these changes result in system divergence from the source of truth. Billing is based on the number of vCPUs/hour in the cluster and is charged monthly. Clusters incur a single charge for configuration management no matter how many repositories are connected.</p>
                        
                        <p>Clusters can function without a constant connection to Azure. When disconnected, each cluster’s charge will be determined based on the last known number of vCPUs that were registered with Azure Arc. If your cluster will be disconnected from Azure and you don’t want to be charged for Kubernetes Configuration, you can delete the configurations. The vCPU count is updated every 5 minutes when connected. The first 6 vCPUs are included at no cost.</p>
                        
                        <table style="width: 100%;margin-top: 20px;">
                            <tr>
                                <th align="left"><b>Services</b></th>
                                <th align="left"><b>Price</b></th>
                                <th align="left"><b>Notes</b></th>
                            </tr>
                            <tr>
                                <td>Azure Policy configuration and Cluster Configuration with GitOps for Arc-enabled Kubernetes</td>
                                <td>￥20/vCPU/month</td>
                                <td>The first 6 vCPU’s are free. It is eligible for Azure Hybrid Benefit Azure Hybrid Benefit - Azure Cloud Computing</td>
                            </tr>
                        </table>

                        <div class="tags-date">
                            <div class="ms-date">
                                <sup>
                                    *
                                </sup>*If the Arc enabled Kubernetes cluster is on Azure Stack Edge, AKS on Azure Stack HCI, or AKS on Windows Server 2019 Datacenter, then Kubernetes configuration is included at no charge. More information on this can be found on <a target="_blank" href="https://docs.azure.cn/en-us/azure-arc/kubernetes/tutorial-use-gitops-connected-cluster">docs page</a>.</p>
                        </div>
                        </div>
                        <p>Other management services such as Azure Policy for Kubernetes, Azure Monitor for Containers and Microsoft Defender for Cloud are in preview so there is no cost to use the service at this time. However, for Azure Policy for Kubernetes and Azure Monitor for Containers, any data retained by those services may incur charges as per the pricing for that service.</p>
                        
                        <h2>Azure Arc-enabled servers</h2>
                        <p>Add-on Azure management services (Azure Update Manager, Azure Policy guest configuration, Azure Monitor, Microsoft Defender for Cloud etc.) are charged for Azure Arc enabled servers when enabled.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="/Static/CSS/Localization/zh-cn.css" rel="stylesheet">
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token =
                '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
                .toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="/Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</div>


</body></html>