<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Power BI Embedded, pricing details, pricing, billing" name="keywords"/>
    <meta content="Learn about the pricing details of Azure Power-Bi-Embedded. A 1RMB Trial gets you ￥1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer."
          name="description"/>
    <title>
        Power BI Embedded Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/power-bi-embedded/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-power-bi-embedded" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/service-banner-power-bi-embedded-slice.png ','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/images/service/power-bi-embedded.png"/>
                                <h2>
                                    Power BI Embedded
                                </h2>
                                <h4>
                                    Embed fully interactive, stunning data visualizations in your applications
                                </h4>
                            </div>
                        </div>
                    </div>



       <div class="technical-azure-selector pricing-detail-tab tab-dropdown" style="margin-top: 40px">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure AI Search
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_storage-blobs">
                Azure Cognitive
                                                    Search
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                <!-- <option data-href="#tabContent1" selected="selected" value="Azure AI Services"> -->
                <option data-href="#tabContent1" selected="selected" value="Power BI Embedded">
                    Azure AI Services
                </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
                Region:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
<span class="selected-item">
China North 3
</span>
                <i class="icon">
                </i>
                <ol class="tab-items">
                    <li class="active">
                        <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                            China North 3
                        </a>
                    </li>
                    <li>
                        <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                            China East 2
                        </a>
                    </li>
                    <li>
                        <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                            China North 2
                        </a>
                    </li>
                    <li>
                        <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                            China East
                        </a>
                    </li>
                    <li>
                        <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                            China North
                        </a>
                    </li>
                </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                <option data-href="#north-china3" selected="selected" value="north-china3">
                    China North 3
                </option>
                <option data-href="#east-china2" value="east-china2">
                    China East 2
                </option>
                <option data-href="#north-china2" value="north-china2">
                    China North 2
                </option>
                <option data-href="#east-china" value="east-china">
                    China East
                </option>
                <option data-href="#north-china" value="north-china">
                    China North
                </option>
            </select>
        </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <div class="tab-panel" id="tabContent1">
          
    
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector" style="min-height: 400px;">
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <!-- END: Product-Detail-TopBanner -->
         <div class="pricing-page-section">
            <p>
                Power BI Embedded allows application developers to embed stunning, fully interactive reports and dashboards into apps
                without the time and expense of having to build your own controls from the ground-up.
            </p>
        </div>
        <!-- BEGIN: TAB-CONTROL -->
        <div class="technical-azure-selector tab-control-selector">
            <!-- BEGIN: TAB-CONTAINER-1 -->
            <div class="tab-control-container tab-active" id="tabContent1">
                <!-- BEGIN: Table1-Content-->
                <h2>
                    Pricing Details
                </h2>
                <p>
                    The total cost of Power BI Embedded depends on the node type chosen and the number of nodes deployed. Node types
                    differ based on number of V-cores and RAM as outlined in the table below:
                </p>
                <div class="tags-date">
                    <div class="ms-date">
                        *The following prices are tax-inclusive.
                    </div>
                    <br/>
                </div>
                <table cellpadding="0" cellspacing="0" width="100%" id="power-bi-embedded-table-a7a8">
                    <tr>
                        <th align="left">
                            <strong>
                                Node type
                            </strong>
                        </th>
                        <!-- <th align="left"><strong>Dedicated capacity</strong></th> -->
                        <th align="left">
                            <strong>
                                Virtual cores
                            </strong>
                        </th>
                        <th align="left">
                            <strong>
                                Memory
                            </strong>
                        </th>
                        <th align="left">
                            <strong>
                                Frontend / Backend cores
                            </strong>
                        </th>
                        <!-- <th align="left"><strong>Peak renders per hour</strong></th> -->
                        <th align="left" width="27%">
                            <strong>
                                Price
                            </strong>
                        </th>
                    </tr>
                    <tr>
                        <td>
                            A1
                        </td>
                        <!-- <td>No</td> -->
                        <td>
                            1
                        </td>
                        <td>
                            3 GB RAM
                        </td>
                        <td>
                            0.5 / 0.5
                        </td>
                        <!-- <td>300</td> -->
                        <td>
                            ￥6.4115/hour
                            <br/>
                            ( about￥4,770.156/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A2
                        </td>
                        <!-- <td>No</td> -->
                        <td>
                            2
                        </td>
                        <td>
                            5 GB RAM
                        </td>
                        <td>
                            1 / 1
                        </td>
                        <!-- <td>600</td> -->
                        <td>
                            ￥12.7715/hour
                            <br/>
                            ( about￥9,501.996/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A3
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            4
                        </td>
                        <td>
                            10 GB RAM
                        </td>
                        <td>
                            2 / 2
                        </td>
                        <!-- <td>1,200</td> -->
                        <td>
                            ￥25.5939/hour
                            <br/>
                            ( about￥19,041.8616/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A4
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            8
                        </td>
                        <td>
                            25 GB RAM
                        </td>
                        <td>
                            4 / 4
                        </td>
                        <!-- <td>2,400</td> -->
                        <td>
                            ￥51.2393/hour
                            <br/>
                            ( about￥38,122.0392/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A5
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            16
                        </td>
                        <td>
                            50 GB RAM
                        </td>
                        <td>
                            8 / 8
                        </td>
                        <!-- <td>4,800</td> -->
                        <td>
                            ￥102.5296/hour
                            <br/>
                            ( about￥76,282.0224/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A6
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            32
                        </td>
                        <td>
                            100 GB RAM
                        </td>
                        <td>
                            16 / 16
                        </td>
                        <!-- <td>9,600</td> -->
                        <td>
                            ￥205.1138/hour
                            <br/>
                            ( about￥15,2604.6672/month )
                        </td>
                    </tr>
                </table>


                <table cellpadding="0" cellspacing="0" width="100%" id="power-bi-embedded-table-hide-a7a8">
                    <tr>
                        <th align="left">
                            <strong>
                                Node type
                            </strong>
                        </th>
                        <!-- <th align="left"><strong>Dedicated capacity</strong></th> -->
                        <th align="left">
                            <strong>
                                Virtual cores
                            </strong>
                        </th>
                        <th align="left">
                            <strong>
                                Memory
                            </strong>
                        </th>
                        <th align="left">
                            <strong>
                                Frontend / Backend cores
                            </strong>
                        </th>
                        <!-- <th align="left"><strong>Peak renders per hour</strong></th> -->
                        <th align="left" width="27%">
                            <strong>
                                Price
                            </strong>
                        </th>
                    </tr>
                    <tr>
                        <td>
                            A1
                        </td>
                        <!-- <td>No</td> -->
                        <td>
                            1
                        </td>
                        <td>
                            3 GB RAM
                        </td>
                        <td>
                            0.5 / 0.5
                        </td>
                        <!-- <td>300</td> -->
                        <td>
                            ￥6.4115/hour
                            <br/>
                            ( about￥4,770.156/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A2
                        </td>
                        <!-- <td>No</td> -->
                        <td>
                            2
                        </td>
                        <td>
                            5 GB RAM
                        </td>
                        <td>
                            1 / 1
                        </td>
                        <!-- <td>600</td> -->
                        <td>
                            ￥12.7715/hour
                            <br/>
                            ( about￥9,501.996/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A3
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            4
                        </td>
                        <td>
                            10 GB RAM
                        </td>
                        <td>
                            2 / 2
                        </td>
                        <!-- <td>1,200</td> -->
                        <td>
                            ￥25.5939/hour
                            <br/>
                            ( about￥19,041.8616/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A4
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            8
                        </td>
                        <td>
                            25 GB RAM
                        </td>
                        <td>
                            4 / 4
                        </td>
                        <!-- <td>2,400</td> -->
                        <td>
                            ￥51.2393/hour
                            <br/>
                            ( about￥38,122.0392/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A5
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            16
                        </td>
                        <td>
                            50 GB RAM
                        </td>
                        <td>
                            8 / 8
                        </td>
                        <!-- <td>4,800</td> -->
                        <td>
                            ￥102.5296/hour
                            <br/>
                            ( about￥76,282.0224/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A6
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            32
                        </td>
                        <td>
                            100 GB RAM
                        </td>
                        <td>
                            16 / 16
                        </td>
                        <!-- <td>9,600</td> -->
                        <td>
                            ￥205.1138/hour
                            <br/>
                            ( about￥15,2604.6672/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A7
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            64
                        </td>
                        <td>
                            200 GB RAM
                        </td>
                        <td>
                            32 / 32
                        </td>
                        <!-- <td>9,600</td> -->
                        <td>
                            ￥410.23/hour
                            <br/>
                            ( about￥305,211.12/month )
                        </td>
                    </tr>
                    <tr>
                        <td>
                            A8
                        </td>
                        <!-- <td>Yes</td> -->
                        <td>
                            128
                        </td>
                        <td>
                            400 GB RAM
                        </td>
                        <td>
                            64 / 64
                        </td>
                        <!-- <td>9,600</td> -->
                        <td>
                            ￥820.47/hour
                            <br/>
                            ( about￥602,989.68/month )
                        </td>
                    </tr>
                </table>

                <!-- <div class="tags-date">
                               <p>Review pricing for Power BI Workspace Collections.</p>
                               <div class="tags-date">
                                   <div class="ms-date">*The following prices are tax-inclusive</div><br />
                               </div>
                               <table cellpadding="0" cellspacing="0" width="100%">
                                   <tr>
                                       <th align="left" width="35%"><strong></strong></th>
                                       <th align="left"><strong>Free</strong></th>
                                       <th align="left"><strong>Standard</strong></th>
                                   </tr>
                                   <tr>
                                       <td>Pricing by Session (every 100 sessions)</td>
                                       <td>The first 100 sessions of every month are free of charge</td>
                                       <td>￥ 32.86 /month</td>

                                   </tr>
                               </table>
                               <div class="tags-date">
                                   <div class="ms-date">*Please note Power BI Workspace Collections will only be available until June 30th, 2019. Learn
                                       more about <a style="font-size:12px"
                                           href="https://docs.azure.cn/en-us/power-bi-embedded/migrate-from-power-bi-workspace-collections">how to
                                           migrate from Power BI Workspace Collections</a></div><br />
                               </div>
                               <p>Use the Power BI Embedded service in your applications that add primary and significant functionality to our service
                                   and are not primarily a substitute for any Power BI service, and are provided for third-party use.</p>
                           </div> -->
                <!-- END: Table1-Content-->
            </div>
            <!-- END: TAB-CONTAINER-1 -->
        </div>
        <!-- END: TAB-CONTROL -->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
         </div>
        </div>
      </div>


                    
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que1">
                                            How often can I change the node type I have deployed?
                                        </a>
                                        <section>
                                            <p>
                                                Customers can change the node type deployed as needed.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que2">
                                            How does the usage of Power BI Embedded show up on my bill?
                                        </a>
                                        <section>
                                            <p>
                                                Power BI Embedded bills on a predictable hourly rate based on the type of node(s) deployed. Actual usage
                                                is computed to the second and billed hourly. For example, if an instance exists for 12 hours and 15
                                                minutes in a month, your bill will show usage of 12.25 hours. If your instance is only active for 6
                                                minutes, your bill will show usage of 0.1 hour.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que3">
                                            Do I need to purchase any additional licenses to publish BI into a Power BI
                                            Embedded node?
                                        </a>
                                        <section>
                                            <p>
                                                Yes, users who publish BI content need to be licensed with Power BI Pro.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que4">
                                            Do users who view BI published through Power BI Embedded need to be licensed
                                            separately?
                                        </a>
                                        <section>
                                            <p>
                                                No, users viewing content do not need Power BI licenses assigned to them.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que5">
                                            What happens when I pause my service?
                                        </a>
                                        <section>
                                            <p>
                                                When the service is paused the embedded content will not load, and you will not be charged for the
                                                service.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que6">
                                            How does Power BI Embedded relate to the Power BI service?
                                        </a>
                                        <section>
                                            <p>
                                                Power BI Embedded is for developers who build applications and want to use a third-party BI offering to
                                                visualize application data instead of building it themselves. Developers embed dashboards and reports
                                                into applications using Power BI Embedded. Power BI, on the other hand, is a software-as-a-service
                                                analytics solution that gives organizations a single view of their most critical business data.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="power-bi-embedded_faq_que7">
                                            Can Power BI Embedded be used to create internal applications?
                                        </a>
                                        <section>
                                            <p>
                                                Yes, however if you plan to use Power BI SaaS embedding you will need to use Power BI Premium through
                                                office (EM1-EM3; P1-P3).
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <div class="pricing-page-section">
                             <h2>Region</h2>
                             <p>Power BI Workspace Collection is available in the following regions:</p>
                             <table cellpadding="0" cellspacing="0" class="table-col6">
                                 <tr>
                                     <th align="left"><strong><strong>Territory</strong></strong></th>
                                     <th align="left"><strong>Region</strong></th>
                                 </tr>
                                 <tr>
                                     <td>Mainland China</td>
                                     <td>China North, China East</td>
                                 </tr>
                             </table> -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="pricing_power-bi-embedded_contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee at least 99.9% availability of Power BI Embedded when users execute API to call or insert reports. To learn
                            more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/power-bi-embedded/" id="pricing_power-bi-embedded_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--

                          <h2>Support &amp; SLA</h2>
                         <p>Azure Support Features:</p>
                         <p>We provide users with the following free support services:</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left"> </th>
                                 <th align="left"><strong>Supported or not</strong></th>
                             </tr>
                             <tr>
                                 <td>Billing and Subscription Management</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Service Dashboard</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web Event Submission</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Unlimited Disruption/Restoration</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Telephone Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP Filing Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>You can <a href="/en-us/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">request support online</a> or contact us via the service hotline.</p>
                         <h2>Service hotline:</h2>
                         <ul>
                             <li>************</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>Community help: <a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">Visit MSDN</a></p>
                         <p>For more support information, please visit <a href="/en-us/support/plans/" id="stor-sla-info">Azure Support Plans</a></p>
                      -->
                    <!-- BEGIN: Product-Detail-BottomBanner -->
                    <!-- END: Product-Detail-BottomBanner -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>

<script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="aWfR98AYkaRrwA_p1HcZD-ZCedK7oMa6gsJVByqkNgYqTI6KBvkQ7pVRUxHN6GIgAaYTov4cEqfEgcfsA45FsYibiCzHlI7XL3yYUpnwZrs1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- <script src='/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js' type='text/javascript'></script> -->
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
