<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure 微软云, Azure 应用程序网关, 负载均衡、 HTTP 负载均衡" name="keywords"/>
  <meta content="了解 Azure 应用程序网关（Azure Application Gateway）的价格详情。使用 Azure 应用程序网关，可以通过提供 HTTP 负载均衡和交付控制来构建高度可缩放且高度可用的网站。基于设置和提供网关的时间量以及应用程序网关处理的数据量对应用程序网关进行收费，提供小型、中型及大型的不同定价。" name="description"/>
  <title>
   应用程序网关定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/application-gateway/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="application-gateway" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/app_gateway.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           应用程序网关
           <span>
            Application Gateway
           </span>
          </h2>
          <h4>
           在 Azure 中生成安全、可缩放且高度可用的 Web 前端
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         通过提供 HTTP 负载平衡和交付控制，Azure 应用程序网关可供您构建高度可扩展，高可用的网站。
        </p>
        <h2>
         定价详细信息
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Application Gateway
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_application-gateway">
                Application Gateway
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Application Gateway">
              Application Gateway
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li>
               <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                中国东部 3
               </a>
              </li>
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china3" value="east-china3">
              中国东部 3
             </option>
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-3 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <div class="tab-content">
           <!-- BEGIN: Table1-Content-->
           <h3>
            应用程序网关
           </h3>
           <p>
            我们基于设置和提供网关的时间量以及应用程序网关处理的数据量对应用程序网关进行收费。
           </p>
           <!-- <p>从2016年4月1日起，价格会下调 25.5%，以下是下调后的新价格：</p> -->
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
            <br/>
            <div class="ms-date">
             *使用多个实例时，按实例收费。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="application-gateway-1" width="100%">
            <tr>
             <th align="left">
              <strong>
               应用程序网关类型
              </strong>
             </th>
             <th align="left">
              <strong>
               基本应用程序网关
              </strong>
             </th>
             <th align="left">
              <strong>
               Web 应用程序防火墙 (WAF) 应用程序网关
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              小型
             </td>
             <td>
              ￥0.16  每个网关/实例/小时（约￥119.04  /月）
             </td>
             <td>
              不可用
             </td>
            </tr>
            <tr>
             <td>
              中型
             </td>
             <td>
              ￥0.45  每个网关/实例/小时（约￥334.80  /月）
             </td>
             <td>
              ￥1.80  每个网关/实例/小时（约￥1339.20  /月）
             </td>
            </tr>
            <tr>
             <td>
              大型
             </td>
             <td>
              ￥2.01  每个网关/实例/小时（约￥1495.44  /月）
             </td>
             <td>
              ￥6.38  每个网关/实例/小时（约￥4746.72  /月）
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="application-gateway-2" width="100%">
            <tr>
             <th align="left">
              <strong>
               应用程序网关类型
              </strong>
             </th>
             <th align="left">
              <strong>
               基本应用程序网关
              </strong>
             </th>
             <th align="left">
              <strong>
               Web 应用程序防火墙 (WAF) 应用程序网关
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              小型
             </td>
             <td>
              ￥0.16  每个网关/实例/小时（约￥119.04  /月）
             </td>
             <td>
              不可用
             </td>
            </tr>
            <tr>
             <td>
              中型
             </td>
             <td>
              ￥0.45  每个网关/实例/小时（约￥334.80  /月）
             </td>
             <td>
              ￥1.80  每个网关/实例/小时（约￥1339.20  /月）
             </td>
            </tr>
            <tr>
             <td>
              大型
             </td>
             <td>
              ￥2.01  每个网关/实例/小时（约￥1495.44  /月）
             </td>
             <td>
              ￥6.38  每个网关/实例/小时（约￥4746.72  /月）
             </td>
            </tr>
           </table>
           <!-- END: Table1-Content-->
           <!-- BEGIN: Table2-Content-->
           <h3>
            数据处理
           </h3>
           <p>
            数据处理收取的费用基于应用程序网关处理的数据量。
           </p>
           <!-- <p>从2016年4月1日起，数据处理的价格会下调 25.5%，以下是下调后的新价格：</p> -->
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              数据处理
             </th>
             <th align="left">
              小型
             </th>
             <th align="left">
              中型
             </th>
             <th align="left">
              大型
             </th>
            </tr>
            <tr>
             <td style="min-width:100px;">
              前 10TB/月
             </td>
             <td style="min-width:100px;">
              ￥0.05/GB
             </td>
             <td style="min-width:50px;">
              免费
             </td>
             <td style="min-width:50px;">
              免费
             </td>
            </tr>
            <tr>
             <td style="min-width:100px;">
              后 30TB(10 - 40 TB)/月
             </td>
             <td style="min-width:100px;">
              ￥0.05/GB
             </td>
             <td style="min-width:50px;">
              ￥0.04/GB
             </td>
             <td style="min-width:50px;">
              免费
             </td>
            </tr>
            <tr>
             <td style="min-width:100px;">
              超过 40TB/月
             </td>
             <td style="min-width:100px;">
              ￥0.05/GB
             </td>
             <td style="min-width:50px;">
              ￥0.04/GB
             </td>
             <td style="min-width:50px;">
              ￥0.02/GB
             </td>
            </tr>
           </table>
           <!-- END: Table2-Content-->
           <!-- BEGIN: Table3-Content-->
           <h3>
            入站数据传输
           </h3>
           <p>
            （即，数据传入 Azure 数据中心）-
            <b>
             免费
            </b>
           </p>
           <!-- END: Table3-Content-->
           <!-- BEGIN: Table4-Content-->
           <h3>
            出站数据传输
           </h3>
           <p>
            （即，数据通过应用程序网关传出 Azure 数据中心）：通过应用程序网关从 Azure 数据中心传出的数据将按照标准的
            <a class="rolecashLink" href="../data-transfer/index.html" id="redis_roleCaching_cloudService" target="_blank">
             数据传输费率
            </a>
            收费。
           </p>
           <!-- END: Table4-Content-->
           <h3>
            应用程序网关v2
           </h3>
           <p>
            Azure 应用程序网关 V2 提供对自动缩放、区域冗余和静态 VIP 的支持。这些网关还提供增强的性能、更好的预配、配置更新时间、标头重写和 WAF 自定义规则。请参阅
            <a href="https://docs.microsoft.com/zh-cn/azure/application-gateway/application-gateway-autoscaling-zone-redundant">
                文档
               </a>以获取其他产品详细信息，并查看下面的常见问题解答部分以了解定价和计费信息。
            
           </p>
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
            <br/>
            <div class="ms-date">
             *以下价格将于2020年9月1日起生效。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="application-gateway-table-standard-v2" width="100%">
            <tr>
             <th align="left">
              <strong>
              </strong>
             </th>
             <th align="left">
                <strong>
                 应用程序网关基本版
                </strong>
               </th>
             <th align="left">
              <strong>
                应用程序网关标准
              </strong>
             </th>
             <th align="left">
              <strong>
                Web 应用程序防火墙
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              固定
             </td>
             <td>
                ¥0.156/网关/小时
               </td>
             <td>
              ¥2.035/网关/小时
             </td>
             <td>
              ¥3.663/网关/小时
             </td>
            </tr>
            <tr>
             <td>
              容量单位
              <sup>
               1
              </sup>
             </td>
             <td>
                ¥0.072/容量单位/小时
               </td>
             <td>
              ¥0.081/容量单位/小时
             </td>
             <td>
              ¥0.147/容量单位/小时
             </td>
            </tr>
           </table>
           <div class="tags-date">
            <div class="ms-date">
             <sup>
              1
             </sup>
             有关容量单位的详细信息，请参阅页面底部的常见问题解答部分。
            </div>
           </div>
          </div>
         </div>
         <!-- END: TAB-CONTAINER-3 -->
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_question1">
             不足一小时的使用是否会按照一个小时计费？
            </a>
            <section>
             <p>
              是的。不足一小时将按照一小时计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_question3">
             是否只为只有一个实例的应用程序云服务提供服务级别协议？小型应用程序网关呢？
            </a>
            <section>
             <p>
              否。小型规模的应用程序网关主要用于开发/测试用途。在小型应用程序网关上运行生产工作负载可能会超过小型实例的处理能力。应用程序网关支持在同一个云服务中部署一个或多个实例。由于缺乏高可用性，不建议运行只有一个实例的应用程序网关云服务。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_faq_application-gateway">
             什么是容量单位？
            </a>
            <section>
             <p>
              容量单位会测量在固定成本之外计费的基于消耗的费用。容量单位费用也按小时或部分按小时计算。容量单位有 3 个维度：计算单位、持久连接和吞吐量。计算单位测量的是所使用的处理器容量。请参阅我们的
              <a href="https://docs.microsoft.com/zh-cn/azure/application-gateway/application-gateway-autoscaling-zone-redundant#pricing">
               文档页
              </a>
              。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="gateway-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证每个具有两个或更多中型或更大型实例的应用程序网关服务至少在 99.9% 的时间内都可用。我们不为只有一个实例或小型实例的应用程序网关服务提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/application-gateway/index.html" id="pricing_gateway_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="as3TvdXr-ppnZH15xZ-h6Fc8DfHUk_WgyDrSCuGtWWs4XZwvI5_H3o76-RO27tSgLEVVYWuosN7c2wSWHDjU_qgJnSrpPiyqIHEcP2fOGKE1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
