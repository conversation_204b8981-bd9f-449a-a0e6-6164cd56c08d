<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure Public IP-Addresses, Pricing page" name="keywords"/>
    <meta content="Learn about the pricing details of Azure IP Addresses." name="description"/>
    <title>
        Azure Public IP Address Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/ip-addresses/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <style>
                        .ip_tit_font {
                            font-size: 16px;
                        }
                    </style>
                    <tags ms.date="09/30/2015" ms.service="en-us-ip-addresses" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/ip-addresses-slice-01.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/media/images/production/ip-addresses-icon.svg"/>
                                <h2>
                                    Public IP Address
                                </h2>
                                <h4>
                                    A dynamic or reserved address used to identify a given Virtual Machine or Cloud Service
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Pricing for Public IP addresses in Azure can vary based on the SKU chosen by the customer – Basic or Standard and the
                            type of IP address – dynamic or static. The table below summarizes the pricing structure for Public IPs.
                        </p>
                        <p>
                            <em>
                                <strong>
                                    Note
                                </strong>
                                : Azure has two different deployment models for creating and working with resources—
                                <a href="https://go.microsoft.com/fwlink/?LinkId=397193&amp;clcid=0x804">
                                    Resource Manager (ARM) and classic (ASM)
                                </a>
                            </em>
                            .
                        </p>
                        <h2>
                            Pricing Details
                        </h2>
                        <!-- BEGIN: Table1-Content-->
                        <!--<h3>保留的 IP 地址</h3>-->
                        <div class="tags-date">
                            <div class="ms-date">
                                *The following prices are tax-inclusive.
                            </div>
                            <br/>
                            <div class="ms-date">
                                *Monthly price estimation is based on a usage of 744 hours every month.
                            </div>
                        </div>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <th align="left">
                                    <strong>
                                        Types
                                    </strong>
                                </th>
                                <th align="left">
                                    <strong>
                                        Basic (Classics)
                                    </strong>
                                </th>
                                <th align="left">
                                    <strong>
                                        Basic (ARM)
                                    </strong>
                                </th>
                                <th align="left">
                                    <strong>
                                        STANDARD (ARM)
                                    </strong>
                                </th>
                            </tr>
                            <tr>
                                <td>
                                    Dynamic IP Address
                                </td>
                                <td>
                                    First cloud service VIP: free of charge
                                    <br/>
                                    Others: ￥0.026/hour (￥19.344/month)
                                    <sup>
                                        1
                                    </sup>
                                </td>
                                <td>
                                    ￥0.026/hour (￥19.344/month)
                                </td>
                                <td>
                                    <p>
                                        N/A
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <p>
                                        Static IP address
                                    </p>
                                </td>
                                <td>
                                    <p>
                                        ¥0.026/hour
                                    </p>
                                </td>
                                <td>
                                    <p>
                                        ¥0.026/hour
                                    </p>
                                </td>
                                <td>
                                    <p>
                                        ¥0.026/hour
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <p>
                                        Public IP prefix
                                        <sup>
                                            2
                                        </sup>
                                    </p>
                                </td>
                                <td>
                                    <p>
                                        N/A
                                    </p>
                                </td>
                                <td>
                                    <p>
                                        N/A
                                    </p>
                                </td>
                                <td>
                                    <p>
                                        ¥0.03816/hour（¥28.391/month）
                                        <sup>
                                            3
                                        </sup>
                                    </p>
                                </td>
                            </tr>
                        </table>
                        <div class="tags-date">
                            <div class="ms-date">
                                <sup>
                                    1
                                </sup>
                                All
                                <a href="https://docs.azure.cn/zh-cn/virtual-network/virtual-networks-instance-level-public-ip">
                                    Instance level public IP addresses
                                </a>
                                (ILPIP) are charged at ￥ 0.026/hour. Each Cloud Service gets a
                                <strong>
                                    free
                                </strong>
                                <a href="https://docs.azure.cn/zh-cn/load-balancer/load-balancer-multivip">
                                    public VIP
                                </a>
                                . Additional VIPs are charged at ￥ 0.026/hour.
                            </div>
                            <br/>
                            <div class="ms-date">
                                <sup>
                                    2
                                </sup>
                                Public IP prefix is a range of contiguous public IP addresses.
                            </div>
                            <br/>
                            <div class="ms-date">
                                <sup>
                                    3
                                </sup>
                                Public IP prefixes are charged per IP per hour. As soon as a prefix is created, you are charged.
                            </div>
                            <br/>
                            <div class="ms-date">
                                The limits imposed on IP addresses are indicated in the full set of
                                <a href="https://docs.azure.cn/zh-cn/azure-subscription-service-limits">
                                    limits for networking
                                </a>
                                in Azure.
                                <a href="https://portal.azure.cn/">
                                    Contact support
                                </a>
                                to increase the default limits based on your business needs (up to the maximum limits).
                            </div>
                            <br/>
                        </div>
                        <!-- END: Table1-Content-->
                        <!-- BEGIN: Table2-Content-->
                    </div>
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question8">
                                            When the virtual machine is in a “stopped-deallocated” status, are you charging me for the public IP address?
                                        </a>
                                        <section>
                                            <p>
                                                In the ARM deployment model, when the relevant VM is in “stopped-deallocated”, no charging is imposed on “dynamic” public
                                                IP addresses. Yet, no matter how relevant resources are, “static” public IP addresses are charged (unless it’s one of the
                                                first 5 “static” public IP addresses in this region). For details about static and dynamic allocation methods, please view
                                                <a href="https://docs.azure.cn/virtual-network/virtual-network-ip-addresses-overview-arm/" id="public_IP_us2"
                                                   style="color: #00a8d9;">
                                                    IP Addresses in Azure
                                                </a>
                                                .
                                            </p>
                                            <p>
                                                In the ARM deployment model, when the VM is in “stopped-deallocated”, no charges are imposed on an instance level public
                                                IP (ILPIP).
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <!--
                                              <li>
                                                  <i class="icon icon-plus"></i>
                                                  <div>
                                                      <a id="public_IP_question1">我可以保留多少个 IP 地址？</a>
                                                      <section>
                                                          <p>Azure 的企业协议，则可以保留最多 100 个 IP 地址。如果您需要增加此限额，请<a id="public_IP_us2" style="color: #00a8d9;" href="/zh-cn/support/contact/">联系我们</a>。</p>
                                                      </section>

                                                  </div>
                                              </li>
                                              -->
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question2">
                                            When does the billing clock for public IP address start and stop?
                                        </a>
                                        <section>
                                            <p>
                                                For the “static” public IP address in the ARM deployment model and the “reserved” IP address in the ASM deployment model,
                                                the billing clock will start from the 2nd hour after you create the IP address so that some time is left to allocate IP
                                                addresses. The billing clock will stop after you delete the IP addresses.
                                            </p>
                                            <p>
                                                For all other IP addresses, the clock will start once relevant resources are activated, or stop when deleting or
                                                stopped-deallocated.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question6">
                                            I purchased Public IP addresses prior to September 2017. Which SKUs do I have?
                                        </a>
                                        <section>
                                            <p>
                                                If you purchased public IP addresses prior to September 2017, you are using the Basic Public IP.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question3">
                                            Is less than 1 hour billed as 1 hour?
                                        </a>
                                        <section>
                                            <p>
                                                Yes. Less than 1 hour is billed at 1 hour.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question4">
                                            What’s an unused reservered IP address?
                                        </a>
                                        <section>
                                            <p>
                                                This is a reserved IP address that is not used in a running deployment. We nominally charge the reserved IP address not
                                                being used (see the pricing table).
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question5">
                                            When the virtual machine is in a stopped-deallocated state, will you charge a certain reserved IP address?
                                        </a>
                                        <section>
                                            <p>
                                                No, not if there is a virtual machine running in the deployment and the reserved IPs include one of 5 reserved IP
                                                addresses currently in use.
                                            </p>
                                            <p>
                                                However, the reserved IP addresses will be charged if all virtual machines in deployment are in a stopped-deallocated
                                                state.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="public_IP_question_price-for-specific-size-of-a-prefix">
                                            What is the price for specific size of a prefix?
                                        </a>
                                        <section>
                                            <p>
                                                The table below shows the per hour charge for prefixes depending on its size.
                                            </p>
                                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            IP BLOCK
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            # OF IPS
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            PER HOUR*
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /31
                                                    </td>
                                                    <td>
                                                        2
                                                    </td>
                                                    <td>
                                                        ￥ 0.07632
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /30
                                                    </td>
                                                    <td>
                                                        4
                                                    </td>
                                                    <td>
                                                        ￥ 0.15264
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /29
                                                    </td>
                                                    <td>
                                                        8
                                                    </td>
                                                    <td>
                                                        ￥ 0.30528
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /28
                                                    </td>
                                                    <td>
                                                        16
                                                    </td>
                                                    <td>
                                                        ￥ 0.61056
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /27
                                                    </td>
                                                    <td>
                                                        32
                                                    </td>
                                                    <td>
                                                        ￥ 1.22112
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /26
                                                    </td>
                                                    <td>
                                                        64
                                                    </td>
                                                    <td>
                                                        ￥ 2.44224
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /25
                                                    </td>
                                                    <td>
                                                        128
                                                    </td>
                                                    <td>
                                                        ￥ 4.88448
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        /24
                                                    </td>
                                                    <td>
                                                        256
                                                    </td>
                                                    <td>
                                                        ￥ 9.76896
                                                    </td>
                                                </tr>
                                            </table>
                                            <p>
                                                *Per hour charge based on individual IP per hour price of ￥ 0.038166 multiplied by the number of IPs within a block.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <!--
                                              <li>
                                                  <i class="icon icon-plus"></i>
                                                  <div>
                                                      <a id="public_IP_question6">保留 IP 和实例层级公共 IP 之间的差别是什么？</a>
                                                      <section>
                                                          <p>一个部署中可能有多个虚拟机实例。 保留的IP是分配给整个部署的公共IP地址，可以被保留不变。实例层级公共 IP 是分配到一个虚拟机级别的公共IP地址，是临时的（非保留，会发生变化）。</p>
                                                      </section>
                                                  </div>
                                              </li>
                                              <li>
                                                  <i class="icon icon-plus"></i>
                                                  <div>
                                                      <a id="public_IP_question7">我是否可以保留实例层级公共 IP 地址？</a>
                                                      <section>
                                                          <p>不可以。目前，您只能保留 VIP。</p>
                                                      </section>
                                                  </div>
                                              </li>
                                              -->
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="ip-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            IP address service doesn’t provide Service Level Agreement. To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/legal/sla/index.html" id="pricing_ip-address_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--
             -->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="4f-BW-zc7fE1bJVytoSzs-61156wvEgoSXg-2ddoc1xkZGGZALhlxqxJM6J8t4UBD5abmgUvKdHDDW0Uk0936DFPW6RUVj7LchGfi4jWy8I1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
