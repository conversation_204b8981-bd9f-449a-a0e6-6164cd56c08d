<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="database migration, microsoft azure, azure" name="keywords"/>
    <meta content="Azure Database Migration Service reduces the complexity of your cloud migration by using a comprehensive," name="description"/>
    <title>
        Pricing - Azure Database Migration Service - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/database-migration/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
    <style>
        .scroll-table:has(.pricing-unavailable-message) {
            display: none;
        }
    </style>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-database-migration" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a, .pure-content .technical-azure-selector p a, .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/database-migration_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Azure Database Migration Service
                                    <span>
            Azure Database Migration Service
           </span>
                                </h2>
                                <h4>
                                    Accelerate your transition to the cloud
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            The Azure Database Migration Service (DMS) is a fully managed service that is designed for both operational database
                            and data warehouse migrations.
                        </p>
                        <p>
                            The Standard pricing tier supports offline (also called “one-time”) migrations. The Standard pricing tier, which
                            offers 1-, 2-, and 4-vCore options, is generally available and free to customers.
                        </p>
                        <p>
                            The Premium pricing tier supports offline and online migrations (also called "continuous migration") for business
                            critical workloads that require minimal downtime. The Premium pricing tier is generally available.
                        </p>
                        <h2>
                            Pricing Details
                        </h2>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Database Migration
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_database-migration">
                                                        Database Migration
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" value="Database Migration">
                                                Database Migration
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China East 2
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                        China East
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                        China North
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#east-china2" selected="selected" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content">
                            <!-- BEGIN: Level 1 tab content panel 1 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 navigator 1 -->
                                <div class="category-container-container">
                                    <div class="category-container-box">
                                        <div class="category-container">
             <span class="category-title hidden-lg hidden-md">
              Category:
             </span>
                                            <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
                                                <li class="active">
                                                    <a data-href="#tabContent1-0" href="javascript:void(0)" id="database-migration-all">
                                                        All
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#tabContent1-1" href="javascript:void(0)" id="database-migration-Standard">
                                                        Standard
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#tabContent1-2" href="javascript:void(0)" id="database-migration-Premium">
                                                        Premium
                                                    </a>
                                                </li>
                                            </ul>
                                            <select class="dropdown-select category-tabs hidden-lg hidden-md">
                                                <option data-href="#tabContent1-0" id="database-migration-all" value="all">
                                                    All
                                                </option>
                                                <option data-href="#tabContent1-1" id="database-migration-Standard" value="Standard">
                                                    Standard
                                                </option>
                                                <option data-href="#tabContent1-2" id="database-migration-Premium" value="Premium">
                                                    Premium
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <!-- END: Tab level 2 navigator 1 -->
                                <!-- BEGIN: Tab level 2 content -->
                                <div class="tab-content">
                                    <!-- BEGIN: Tab level 2 content 1-1 -->
                                    <div class="tab-panel" id="tabContent1-1">
                                        <h3>
                                            Standard
                                        </h3>
                                        <p>
                                            Most small- to medium- business workloads (supports offline migration only)
                                        </p>
                                        <h4>
                                            Compute
                                        </h4>
                                        <p>
                                            Compute is provisioned in virtual cores (vCores). A vCore represents a logical CPU.
                                        </p>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                *The following prices are tax-inclusive.
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *Monthly price estimation is based on a usage of 31 days per month.
                                            </div>
                                        </div>
                                        <table cellpadding="0" cellspacing="0" id="database-migration-standard-compute" width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        vCore
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Price
                                                        <sup>
                                                            *
                                                        </sup>
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    1
                                                </td>
                                                <td>
                                                    Free
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    2
                                                </td>
                                                <td>
                                                    Free
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    4
                                                </td>
                                                <td>
                                                    Free
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="tab-panel" id="tabContent1-2">
                                        <h3>
                                            Premium
                                        </h3>
                                        <p>
                                            Large or business critical workloads (supports online migrations, offline migrations, and faster migration speeds)
                                        </p>
                                        <h3>
                                            Compute
                                        </h3>
                                        <p>
                                            Compute is provisioned in virtual cores (vCores). A vCore represents a logical CPU.
                                        </p>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                *The following prices are tax-inclusive.
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *Monthly price estimation is based on a usage of 31 days per month.
                                            </div>
                                        </div>
                                        <table cellpadding="0" cellspacing="0" id="database-migration-premium-compute" width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        VCore
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Price
                                                        <sup>
                                                            *
                                                        </sup>
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    4
                                                </td>
                                                <td>
                                                    ￥ 1.9/hour
                                                    <br/>
                                                    (about￥ 1413.6/month)
                                                </td>
                                            </tr>
                                        </table>
                                        <!-- END: Table1-Content-->
                                    </div>
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            <sup>
                                                *
                                            </sup>
                                            DMS Premium 4-vCore is free for 6 months (183 days) from the DMS service creation date before incurring any charges.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- END: TAB-CONTAINER-1 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <h3>
                                Overview
                            </h3>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="home_database-migration_How is my bill calculated for the Standard tier of Database Migration Service">
                                            How is my bill calculated for the Standard tier of Database Migration Service?
                                        </a>
                                        <section>
                                            <p>
                                                The Standard tier of Database Migration Service supports offline migrations and is free to use.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="home_database-migration_How is my bill calculated for the Premium tier of Database Migration Service">
                                            How is my bill calculated for the Premium tier of Database Migration Service?
                                        </a>
                                        <section>
                                            <p>
                                                DMS Premium tier is billed on a predictable, hourly rate based on provisioned compute in vCore for your migration
                                                service.
                                            </p>
                                            <p>
                                                You can use 4 vCore Premium DMS for free for 6 months (183 days) from DMS service creation before billing starts.
                                            </p>
                                            <p>
                                                For example:
                                            </p>
                                            <ul>
                                                <li>
                                                    If you use 4 vCore Premium DMS for 6 months (183 days) or less from the time of DMS service creation, you will
                                                    not be billed (DMS Premium 4 vCore is free for 183 days from DMS service creation). So if you use 4 vCore
                                                    Premium DMS for 3 months, it will be free.
                                                </li>
                                                <li>
                                                    If you create a 4 vCore Premium DMS service on March 15, your 6-month (183 days) free period will start when you
                                                    create a DMS service on March 15 and will continue until September 14. For usage occurring on or after September
                                                    15, you will be billed per hour for the compute resources you provision. If you use 4 vCore Premium DMS from
                                                    March 15 to November 1, you will have free usage until September 14 (183 days) and then will be billed for usage
                                                    from September 15 to November 1.
                                                </li>
                                            </ul>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="home_database-migration_How long can I use the Premium tier of Database Migration Service">
                                            How long can I use the Premium tier of Database Migration Service?
                                        </a>
                                        <section>
                                            <p>
                                                Once you create a DMS service, you can use DMS for up to 1 year from the date of service creation.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="home_database-migration_How many DMS services can I create">
                                            How many DMS services can I create?
                                        </a>
                                        <section>
                                            <p>
                                                Customers can create 2 DMS services per subscription. To create additional services, please create a support ticket.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="database-migration-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/legal/sla" id="pricing_database-migration_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="P5UuDwu4VZjLKZekNjZtrNr7c-EWNWu59sUBym6rXqasIIqD_wlcpCiQEEVysZuSExBnJnEKj5RMDZ8XHDMmNAt5HnDBYSThLSiBEHxTYco1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
