<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="" name="keywords"/>
    <meta content="Publish, manage, secure, and analyze your API in minutes" name="description"/>
    <title>
        API Management Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/api-management/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="hide-info" style="display:none;">
    <div class="bg-box">
        <div class="cover-bg">
        </div>
    </div>
    <div class="msg-box">
        <div class="pricing-unavailable-message">
            Not available in the selected region
        </div>
    </div>
</div>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-api-management" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/product-banner-api-01.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/images/<EMAIL>"/>
                                <h2>
                                    API Management
                                </h2>
                                <h4>
                                    Publish APIs to developers, partners, and employees securely and at scale
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure API Management allows organizations to publish APIs more securely, reliably, and at scale. Use API Management
                            to enable internal teams, partners, and developers to use APIs while benefiting from the business and log analytics
                            provided by the admin portal. This service helps provide the tools your organization needs for end-to-end API
                            management – everything from provisioning user roles, creating usage plans and quotas, and applying policies for
                            transforming payloads, to throttling, analytics, monitoring, and alerts.
                        </p>
                    </div>
                    <h2>
                        Pricing Details
                    </h2>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              API Management
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_storage-blobs">
                                                        API Management
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="API Management">
                                                API Management
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China East 2
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li>
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>
                                                <li class="active">
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                        China East
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                        China North
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#east-china2" selected="selected" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>
                                            <option data-href="#north-china3" value="north-china3">
                                                China North3
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-content">
                            <div class="tab-panel" id="tabContent1">
                                <div class="pricing-page-section" style="margin-top: 0;">
                                    <!-- BEGIN: Table1-Content-->
                                    <h2>
                                        API Management
                                    </h2>
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            *The following prices are tax-inclusive.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            *Monthly pricing estimates are based on 744 hours of usage per month.
                                        </div>
                                        <br/>
                                    </div>
                                    <table cellpadding="0" cellspacing="0" id="API-Management-preview" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Developer
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Basic
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Standard
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Premium
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Purpose
                                            </td>
                                            <td>
                                                Non-production use cases and evaluations
                                            </td>
                                            <td>
                                                Entry-level production use cases
                                            </td>
                                            <td>
                                                Medium-volume production use cases
                                            </td>
                                            <td>
                                                High-volume or Enterprise production use cases
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Price (per unit)
                                            </td>
                                            <td>
                                                ￥0.5022/hour (about ￥373.6368/month)
                                            </td>
                                            <td>
                                                ￥1.5386/hour (about ￥1,144.7184/month)
                                            </td>
                                            <td>
                                                ￥7.1794/hour (about ￥5,341.4736/month)
                                            </td>
                                            <td>
                                                ￥29.2229/hour (about ￥21,741.8376/month)
                                                <br/>
                                                Unit cost of incremental units (&gt;1)
                                                <br/>
                                                charged at 50% of the first unit purchased
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Cache (per unit)
                                            </td>
                                            <td>
                                                10 MB
                                            </td>
                                            <td>
                                                50 MB
                                            </td>
                                            <td>
                                                1 GB
                                            </td>
                                            <td>
                                                5 GB
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Scale-out (units)
                                            </td>
                                            <td>
                                                1
                                            </td>
                                            <td>
                                                2
                                            </td>
                                            <td>
                                                4
                                            </td>
                                            <td>
                                                10 per region (call support to add more)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                SLA
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                99.9%
                                            </td>
                                            <td>
                                                99.9%
                                            </td>
                                            <td>
                                                99.95%
                                                <sup>
                                                    1
                                                </sup>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Usage limits
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Cache, external
                                                <sup>
                                                    6
                                                </sup>
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Developer portal
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Multiple custom domain names
                                                <sup>
                                                    5
                                                </sup>
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Azure Active Directory integration
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Virtual Network support
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Multi-region deployment
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Self-hosted gateway
                                            </td>
                                            <td>
                                                Yes
                                                <sup>
                                                    3
                                                </sup>
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                                <sup>
                                                    4
                                                </sup>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td>
                                                Workspaces gateway
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>


                                        <tr>
                                            <td>
                                                Estimated maximum throughput
                                                <sup>
                                                    2
                                                </sup>
                                                (per unit)
                                            </td>
                                            <td>
                                                500 requests/second
                                            </td>
                                            <td>
                                                1,000 requests/second
                                            </td>
                                            <td>
                                                2,500 requests/second
                                            </td>
                                            <td>
                                                4,000 requests/second
                                            </td>
                                        </tr>
                                    </table>
                                    <table cellpadding="0" cellspacing="0" id="API-Management-preview2" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Developer
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Basic
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Standard
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Purpose
                                            </td>
                                            <td>
                                                Non-production use cases and evaluations
                                            </td>
                                            <td>
                                                Entry-level production use cases
                                            </td>
                                            <td>
                                                Medium-volume production use cases
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Price (per unit)
                                            </td>
                                            <td>
                                                ￥0.5022/hour (about ￥373.6368/month)
                                            </td>
                                            <td>
                                                ￥1.5386/hour (about ￥1,144.7184/month)
                                            </td>
                                            <td>
                                                ￥7.1794/hour (about ￥5,341.4736/month)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Cache (per unit)
                                            </td>
                                            <td>
                                                10 MB
                                            </td>
                                            <td>
                                                50 MB
                                            </td>
                                            <td>
                                                1 GB
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Scale-out (units)
                                            </td>
                                            <td>
                                                1
                                            </td>
                                            <td>
                                                2
                                            </td>
                                            <td>
                                                4
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                SLA
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                99.9%
                                            </td>
                                            <td>
                                                99.9%
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Usage limits
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Cache, external
                                                <sup>
                                                    6
                                                </sup>
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Developer portal
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Multiple custom domain names
                                                <sup>
                                                    5
                                                </sup>
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Azure Active Directory integration
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Virtual Network support
                                            </td>
                                            <td>
                                                Yes
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Multi-region deployment
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Self-hosted gateway
                                            </td>
                                            <td>
                                                Yes
                                                <sup>
                                                    3
                                                </sup>
                                            </td>
                                            <td>
                                                No
                                            </td>
                                            <td>
                                                No
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Estimated maximum throughput
                                                <sup>
                                                    2
                                                </sup>
                                                (per unit)
                                            </td>
                                            <td>
                                                500 requests/second
                                            </td>
                                            <td>
                                                1,000 requests/second
                                            </td>
                                            <td>
                                                2,500 requests/second
                                            </td>
                                        </tr>
                                    </table>
                                    <div class="tags-date">
                                        <p>
                                        <div class="ms-date">
                                        <sup>
                                         1
                                        </sup>
                                        Requires deployment of at least one unit in two or more regions.
                                        </div>    
                                        </p>
                                        <p>
                                        <div class="ms-date">
                                        <sup>
                                         2
                                        </sup>
                                        Throughput figures are presented for information only and must not be relied upon for capacity and budget planning. Load test reflecting anticipated production conditions must be conducted to determine expected throughput accurately.
                                        </div>    
                                        </p>
                                        <p>
                                        <div class="ms-date">
                                        <sup>
                                         3
                                        </sup>
                                        In the Developer tier, the self-hosted gateway feature is available at no additional cost. The number of gateway deployments is not limited, but each deployment is limited to a single gateway replica (instance). 
                                        </div>    
                                        </p>
                                        <p>
                                        <div class="ms-date">
                                        <sup>
                                         4
                                        </sup>
                                        In the Premium tier, the self-hosted gateway feature is available at an additional cost (see below). The number of gateway replicas (instances) in each deployment is not limited.
                                        </div>    
                                        </p>
                                        <p>
                                        <div class="ms-date">
                                       <sup>
                                         5
                                        </sup>
                                        Available for gateway only. Limit of 20 domain names per instance.  
                                        <a href="https://support.azure.cn/en-us/support/contact/" style="float: none; font-size: 12px ; display: inline; background-color: white; margin:0; padding:0;" aria-label="aLabel" target="_blank">
                                         Call Support
                                        </a>
                                        to add more.
                                        </div>
                                        </p>
                                        <p>
                                        <div class="ms-date">
                                        <sup>
                                         6
                                        </sup>
                                        Bring your own Redis-compatible cache, e.g. Azure Redis Cache.
                                        </div>
                                        </p>
                                        </div>                      
                                </div>
                                <div class="pricing-page-section">
                                    <h2>
                                        Gateway
                                    </h2>
                                    <p>
                                        The gateway resource ensures runtime isolation and supports hybrid and multi-cloud API deployments. 
                                        <a href="https://docs.azure.cn/en-us/api-management/api-management-gateways-overview" style="float: none; font-size: 15px ; display: inline; background-color: white; margin:0; padding:0;" aria-label="aLabel" target="_blank">Learn more about gateways.</a>
                                    </p>
                                    <table cellpadding="0" cellspacing="0" id="API-Management-gateway" width="100%">
                                        <tr>
                                         <th align="left">
                                          <strong>
                                          </strong>
                                         </th>
                                         <th align="left">
                                          <strong>
                                            Workspace gateway premium
                                          </strong>
                                         </th>
                                         <th align="left">
                                          <strong>
                                            Self-hosted gateway
                                          </strong>
                                         </th>
                                        </tr>
                                        <tr>
                                         <td>
                                          Purpose
                                         </td>
                                         <td>
                                            Use as a managed data plane for workspaces in the Premium tier.
                                         </td>
                                         <td>
                                            Use as a self-hosted data plane for APIs deployed on premises or in other private and public clouds.
                                         </td>
                                        </tr>
                                        <tr>
                                         <td>
                                          Price
                                         </td>
                                         <td>
                                          ￥ 12,235.08 per month per gateway unit
                                         </td>
                                         <td>
                                          ￥ 2,060.88 per month per gateway deployment
                                          <sup>1</sup>
                                         </td>
                                        </tr>
                                        <tr>
                                         <td>
                                            Workspaces
                                         </td>
                                         <td>
                                            ￥647.28 for 5 workspaces per month<sup>2</sup>
                                         </td>
                                         <td>
                                            N/A
                                         </td>
                                        </tr>
                                    </table>
                                    <div class="tags-date">
                                        <p>
                                        <div class="ms-date">
                                        <sup>
                                         1
                                        </sup>
                                        Developer tier deployments are free of charge.
                                        </div>    
                                        </p>
                                        <p>
                                            <div class="ms-date">
                                                <sup>
                                                    2
                                                   </sup>
                                                   The first five workspaces are included at no additional cost.
                                            </div>
                                        </p>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="api-management_faq-01">
                                            What is the purpose of the Developer tier?
                                        </a>
                                        <section>
                                            <p>
                                                The Developer tier is suitable for API Management trial, development, and functional testing. Customers
                                                should not use this tier for production.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="api-management_faq-02">
                                            Can I deploy the API Management proxy on-premises in my own data center?
                                        </a>
                                        <section>
                                            <p>
                                                No. There is no on-premises deployment option available at this time, but you can vote on
                                                <a href="https://feedback.azure.com/forums/248703-api-management?filter=top&amp;page=1"
                                                   id="api-management_faq-feedback-api-management">
                                                    UserVoice
                                                </a>
                                                if you’d like this capability. However,
                                                you can certainly use Azure-based API management with on-premises systems and data.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="api-management_faq-03">
                                            What is a “unit” and how can I scale my service?
                                        </a>
                                        <section>
                                            <p>
                                                Customers can scale API Management by adding and removing units. Each unit has capacity that depends on its
                                                tier. For example, the standard tier includes a throughput of 200 million API calls per month, 1 TB of
                                                bandwidth per month, and approximately 1,000 requests per second. As you add additional units, capacity
                                                scales proportionally. For example, two standard units provide 400 million API calls per month, 2 TB of
                                                bandwidth, and approximately 2,000 requests per second.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="api-management_faq-04">
                                            What is a “gateway deployment”?
                                        </a>
                                        <section>
                                            <p>
                                                All nodes within the gateway deployment share the location properties and configuration, for example, the custom domain
                                                name(s) and assigned APIs. Each gateway deployment corresponds to a gateway resource that can be created either via the
                                                Azure portal on the Gateways blade inside an API Management service or programmatically via management API.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="cognitive-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee that API Management Service instances running in the Basic, Standard and Premium tiers will respond to
                            requests to perform operations at least 99.9% of the time.No SLA is provided for the Developer tier of the API
                            Management Service. If you want to learn more about the details of our server level agreement, please visit the
                            <a href="/en-us/support/sla/api-management/" id="pricing_api-management_sla">
                                Service Level Agreement
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static//Scripts/lib/jquery-1.12.3.min.js">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="ep1Y716elbXbpx_787OKE6dR2Eh0BD4QZZ19ffv0rvQz7W0pmZi4jG9-IShD-QAjxLgVnR2lRlBr73X5c1Hzz3EFoHYwpxfbYA5sKYdKR6U1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
