<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, 网络观察程序，网络，虚拟网络" name="keywords"/>
  <meta content="了解 Azure 网络观察程序（Network Watcher）价格详情。使用网络观察程序可监视和诊断网络的运行状况和性能。每个订阅可以跨所有区域创建最多 50 个虚拟网络。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   网络观察程序定价 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/network-watcher/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="network-watcher" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/network-watcher.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           网络观察程序
           <span>
            Network Watcher
           </span>
          </h2>
          <h4>
           网络性能监视和诊断解决方案
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         使用网络观察程序可监视和诊断网络的运行状况和性能。例如，网络观察程序诊断和可视化工具可帮助捕获虚拟机上的数据包，验证是否接受或拒绝某个 IP 流。还可以标识数据包将从虚拟机路由到的位置，并深入了解整个网络拓扑。
        </p>
        <h2>
         定价详细信息
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Network Watcher
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_network-watcher">
                Network Watcher
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Network Watcher">
              Network Watcher
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-3 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <div class="tab-content">
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="network-watcher-region" width="100%">
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               内附免费单位数
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              收集的网络日志
              <sup>
               1
              </sup>
             </td>
             <td>
              每月 5GB
             </td>
             <td>
              ￥5.09/GB
             </td>
            </tr>
            <tr>
             <td>
              网络诊断工具
              <sup>
               2
              </sup>
             </td>
             <td>
              1,000 次检查/月
             </td>
             <td>
              ￥10.00/1,000 次检查
             </td>
            </tr>
            <tr>
             <td>
              连接监视器
             </td>
             <td>
              每月 10 次测试
             </td>
             <td>
              0-10 次测试 - 包含
              <br/>
              10-240,010 次测试 - 每月 ￥3.054  次测试
              <br/>
              240,010-750,010 次测试 - 每月 ￥1.017 次测试
              <br/>
              750,010-1,000,010 次测试 - 每月 ￥0.509次测试
              <br/>
              超过 1,000,010 次测试 - 每月 ￥0.203次测试
              <br/>
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="network-watcher-region2" width="100%">
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               内附免费单位数
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              收集的网络日志
              <sup>
               1
              </sup>
             </td>
             <td>
              每月 5GB
             </td>
             <td>
              ￥5.09/GB
             </td>
            </tr>
            <tr>
             <td>
              网络诊断工具
              <sup>
               2
              </sup>
             </td>
             <td>
              1,000 次检查/月
             </td>
             <td>
              ￥10.00/1,000 次检查
             </td>
            </tr>
            <tr>
             <td>
              连接监视器
             </td>
             <td>
              每月 10 次测试
             </td>
             <td>
              0-10 次测试 - 包含
              <br/>
              10-240,010 次测试 - 每月 ￥3.054  次测试
              <br/>
              240,010-750,010 次测试 - 每月 ￥1.017 次测试
              <br/>
              750,010-1,000,010 次测试 - 每月 ￥0.509次测试
              <br/>
              超过 1,000,010 次测试 - 每月 ￥0.203次测试
              <br/>
             </td>
            </tr>
            <tr>
             <td>
              流量分析
             </td>
             <td>
              ——
             </td>
             <td>
              <p>
               间隔10分钟数据处理：每处理 1GB ￥35.61
               <sup>
                3
               </sup>
              </p>
              <p>
               间隔60分钟数据处理：每处理 1GB ￥23.405
               <sup>
                4
               </sup>
              </p>
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="network-watcher-region3" width="100%">
            <tr>
             <th align="left">
                <strong>
                 功能
                </strong>
             </th>
             <th align="left">
                <strong>
                内附免费单位数
                </strong>
             </th>
             <th align="left">
                <strong>
                价格
                </strong>
             </th>
            </tr>
            <tr>
                <td>
                    已收集的网络日志 (NSG流日志) 
                    <sup>1</sup>
                </td>
                <td>
                    每月 5GB
                </td>
                <td>
                    ￥5.09/GB
                </td>
            </tr>
            <tr>
                <td>
                    已收集虚拟网络流日志 
                </td>
                <td>
                    N/A
                </td>
                <td>
                    ￥3.18/GB
                </td>
            </tr>
            <tr>
                <td>
                    网络诊断工具
                    <sup>2</sup>
                </td>
                <td>
                    1,000 次检查/月
                </td>
                <td>
                    ￥10.00/1,000 次检查
                </td>
            </tr>
            <tr>
                <td>
                连接监视器
                </td>
                <td>
                每月 10 次测试
                </td>
                <td>
                0-10 次测试 - 包含
                <br/>
                10-240,010 次测试 - 每月 ￥3.054  次测试
                <br/>
                240,010-750,010 次测试 - 每月 ￥1.017 次测试
                <br/>
                750,010-1,000,010 次测试 - 每月 ￥0.509次测试
                <br/>
                超过 1,000,010 次测试 - 每月 ￥0.203次测试
                <br/>
                </td>
            </tr>
            <tr>
                <td>
                    流量分析
                </td>
                <td>
                    --
                </td>
                <td>
                    间隔10分钟数据处理：每处理 1GB ￥35.61
                    <sup>3</sup>
                    <br>
                    间隔60分钟数据处理：每处理 1GB ￥23.405
                    <sup>4</sup>
                </td>
            </tr>
           </table>
           <div class="tags-date">
            <div class="ms-date">
             <sup>
              1
             </sup>
             网络日志存储在存储帐户内，并且具有可设置为 1 至 365 天的保留策略。如果未设置保留策略，日志将一直保留。将分别对存储和事件中心收取相应的费用。
            </div>
            <br/>
            <div class="ms-date">
             <sup>
              2
             </sup>
             对网络观察程序诊断工具和拓扑功能的收费基于 Azure 门户、PowerShell、CLI 或 Rest 发起的网络诊断检查次数。
            </div>
            <br/>
            <div class="ms-date">
             <sup>
              3
             </sup>
             间隔10分钟数据处理的正式商用价格将于2020年1月1日开始生效。
            </div>
            <br/>
            <div class="ms-date">
             <sup>
              4
             </sup>
             流量分析功能针对服务处理的日志数据和引入 Azure Log Analytics 服务的结果数据计费。还会对引入
             <a href="../monitor/index.html">
              Azure Log Analytics
             </a>
             服务的日志收取相应费用。
            </div>
           </div>
          </div>
         </div>
         <!-- END: TAB-CONTAINER-3 -->
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="network-watcher-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证在 99.9％ 的时间内，网络诊断工具会成功执行并返回响应。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/network-watcher/index.html" id="pricing_network-watcher-watcher_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="iUzJ5_BUJaMu9dJKR9k68_lRZPj7emU2mSNQwjflMz6e1l5iPZbr0Xt255HywYZY7RypjWa7Obwhtbp_zYxM52Y9wMIcRDZPZ8iv7TJtRSw1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
