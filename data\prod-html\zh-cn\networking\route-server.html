

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <meta name="keywords" content="Azure, 微软云, Azure 容器注册表, Contariner Registry, 价格详情, 定价, 计费" />
    <meta name="description" content="Azure 容器注册表是用于为所有 Azure 容器部署的公用存储管理专用 Docker 注册表的服务。立即开始免费试用。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" />

    <title>Azure路由服务器价格 -  Azure云服务</title>

    <link rel="apple-touch-icon" sizes="180x180" href="../../../Static/Favicon/apple-touch-icon.png">
    <link rel="shortcut icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="manifest" href="../../../Static/Favicon/manifest.json">
    <link rel="mask-icon" href="../../../Static/Favicon/safari-pinned-tab.svg" color="#0078D4">
    <meta name="msapplication-config" content="/Static/Favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
    <link rel="canonical" href="https://azure.microsoft.com/pricing/details/route-server/"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    
    <!-- END: Page Style -->

    <!-- BEGIN: Minified Page Style -->
    <link href='../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css' rel='stylesheet' />
    <!-- END: Minified Page Style -->
            
    <link rel="stylesheet" href="../../../StaticService/css/service.min.css" />
</head>
<body class="zh-cn">    
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    
    <style>
       @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
        .updatetime {
            color: black;
            text-align: right;
            font-size: 12px;
        }
    </style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage"></div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li><span></span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select><option selected="selected">加载中...</option></select>
                        <span class="icon icon-arrow-top"></span>
                    </div>
                
                    
                    <tags ms.service="route-server" ms.date="12/10/2018" wacn.date="12/10/2018"></tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/container-registry_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/resources/self-serve/private-link.svg" />
                                <h2>Azure路由服务器<span>Route Server</span></h2>
                                <h4>简化网络设备的操作管理</h4>
                                <div class = "updatetime">更新时间：2025年08月05日</div>                                
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->   
            
                    <div class="pricing-page-section"></div>
            
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown" style="overflow-y: auto;">

                        <!-- BEGIN: REGION SELECTED -->
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>OS/软件:</label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                            <span class="selected-item">Backup</span>
                                            <i class="icon"></i>
                                            <ol class="tab-items">
                                                <li class="active"><a href="javascript:void(0)" data-href="#tabContent1" id="home_backup">Backup</a></li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option selected="selected" data-href="#tabContent1" value="Backup">Backup</option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container" style="display:none;">
                                        <label>地区：</label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                            <span class="selected-item">中国东部3</span>
                                            <i class="icon"></i>
                                            <ol class="tab-items">
                                                <li class="active"><a href="javascript:void(0)" data-href="#east-china3" id="east-china3">中国东部 3</a></li>
                                                <li><a href="javascript:void(0)" data-href="#north-china3" id="north-china3">中国北部 3</a></li>
                                                <li><a href="javascript:void(0)" data-href="#east-china2" id="east-china2">中国东部 2</a></li>
                                                <li><a href="javascript:void(0)" data-href="#north-china2" id="north-china2">中国北部 2</a></li>
                                                <li><a href="javascript:void(0)" data-href="#east-china" id="east-china">中国东部</a></li>
                                                <li><a href="javascript:void(0)" data-href="#north-china" id="north-china">中国北部</a></li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                            <!-- <option selected="selected" data-href="#east-china3"
                                                    value="east-china3">中国东部 3
                                            </option> -->
                                            <option data-href="#north-china3" value="north-china3">中国北部 3
                                            </option>
                                            <option selected="selected" data-href="#east-china2"
                                                    value="east-china2">中国东部 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">中国北部 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">中国东部</option>
                                            <option data-href="#north-china" value="north-china">中国北部</option>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        <!-- END: REGION SELECTED -->
                        
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content" id="tabContent1">
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Table1-Content-->                            
                                <div class="scroll-table" style="display: block;">
                                    <p>通过 Azure 路由服务器，网络设备可以通过 Azure 虚拟网络动态交换路由信息。配置网络设备和 Azure ExpressRoute 以及 VPN 网关，自动从 Azure 路由服务器获取最新的路由信息，而不是手动与每个网络通信。</p>
                                    <table cellpadding="0" cellspacing="0" width="100%">
                                        <thead>
                                            <tr>
                                                <th align="left" width="50%">&nbsp;</th>
                                                <th align="left" width="50%"><strong>价格</strong></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="left_align">Azure 路由服务器</td>
                                                <td class="left_align">￥4.58/小时</td>
                                            </tr>
                                            <tr>
                                                <td class="left_align">路由基础结构单元</td>
                                                <td class="left_align">￥1.0176/小时</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!-- END: Table1-Content-->
                            </div>
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                    </div>
                    <!-- END: TAB-CONTROL -->

                    <div class="pricing-page-section">
                        <h2>支持和服务级别协议</h2>
                        <p>如有任何疑问或需要帮助，请访问<a href="https://support.azure.cn/zh-cn/support/contact" id="app-contact-page"> Azure 支持</a>选择自助服务或者其他任何方式联系我们获得支持。</p>
                        <p>若要了解有关我们的服务器级别协议的详细信息，请访问<a href="../../../support/sla/route-server/" id="pricing_route-server_sla">服务级别协议</a>页。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->

<!-- BEGIN: Footer -->
<div class="public_footerpage"></div>
<!--END: Common sidebar-->


        
<link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet">
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="FtJS7EhZBT6rgbp0PkQRqZsvT4EkKorxier871BisK9SjwrzF-3NCsrqEl_VYuhENbkKwc9cRYh6MY_Z2AzDIMXeWXd32LE1Vl7rUDgXkV41" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>

<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>

<!-- BEGIN: Minified RequireJs -->
<script src='../../../Static/Scripts/global.config.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/require.js' type='text/javascript'></script>
<script src=' /Static/Scripts/pricing-page-detail.js' type='text/javascript'></script>
<script src='../../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js' type='text/javascript'></script>

<!-- END: Minified RequireJs -->
            
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js"></script>

<script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
</script>
<!-- end JSLL -->

<script type="text/javascript" src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../Static/Scripts/wacndatatracker.js"></script>


<script type="text/javascript" src="/common/useCommon.js"></script>

</body>
</html>