<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure 存储 - 文件,价格详情, 定价, 计费" name="keywords"/>
  <meta content="Azure 文件在云端提供完全托管的文件共享，这些共享项可通过行业标准的服务器消息块 (SMB) 协议进行访问。" name="description"/>
  <title>
   价格详情-Azure Arc
  </title>
  <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/storage/files/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }

    .pricing-detail-tab .tab-nav {
        padding-left: 0 !important;
        margin-top: 5px;
        margin-bottom: 0;
        overflow: hidden;
    }

    .dricePrice {
        margin-top: 10px;
        float: left;
    }

    .pricing-detail-tab .tab-nav li {
        list-style: none;
        float: left;
    }

    .pricing-detail-tab .tab-nav li.active a {
        border-bottom: 4px solid #00a3d9;
    }

    .pricing-detail-tab .tab-nav li.active a:hover {
        border-bottom: 4px solid #00a3d9;
    }

    .pricing-detail-tab .tab-content .tab-panel {
        display: none;
    }

    .pricing-detail-tab .tab-content .tab-panel.show-md {
        display: block;
    }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="core-control" wacn.date="11/27/2015">
       </tags>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/storage.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
            <img src="/Static/Images/SVG/00756-icon-service-Azure-Arc.svg" alt="">
          <h2>
            Azure Arc
            <span>Azure Arc 是扩展 Azure 平台的桥梁，可帮助你构建可以灵活地跨数据中心、在边缘和在多云环境中运行的应用程序和服务。</span>
          </h2>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>已启用 Azure Arc 的 Kubernetes</h2>
        <p>已启用 Azure Arc 的 Kubernetes 使客户能够跨客户数据中心、边缘位置和多云连接和配置 Kubernetes 群集。</p>
        <p>
可以在 Azure 门户中免费连接和组织任意数量的 Kubernetes 群集。就像已启用 Arc 的服务器一样，启用附加 Azure 管理服务（例如 Kubernetes 配置）后需为其付费。

        </p>
        <h2>Kubernetes 配置</h2>
        <p>Kubernetes 配置使用 GitOps 提供配置管理和应用程序部署。利用此功能，群集管理员可以在 Git 中声明其群集配置和应用程序。然后，开发团队可以使用拉取请求和常用工具（现有 DevOps 管道、Git、Kubernetes 清单、Helm 图表）将应用程序轻松部署到已启用 Azure Arc 的 Kubernetes 群集并在生产环境中进行更新。GitOps 代理会侦听更改，并在这些更改导致系统偏离事实来源时促进自动回滚。计费基于群集中的每小时 vCPU 数，按月收费。无论连接了多少存储库，群集都只需支付一次配置管理费用。</p>
        <p>群集可以在不与 Azure 保持持续连接的情况下正常运行。断开连接后，将根据在 Azure Arc 中注册的最后已知 vCPU 数量来确定每个群集的费用。如果群集将从 Azure 断开连接，并且不希望 Kubernetes 配置产生费用，则可以删除配置。连接后，vCPU 计数会每隔 5 分钟更新一次。前 6 个 vCPU 免费提供。</p>
        
        <table style="width: 100%;margin-top: 20px;">
            <tr>
                <th align="left"><b>服务</b></th>
                <th align="left"><b>价格</b></th>
                <th align="left"><b>备注</b></th>
            </tr>
            <tr>
                <td>使用已启用 Arc 的 Kubernetes 的 GitOps 的 Azure Policy 配置和群集配置</td>
                <td>￥20/vCPU/月</td>
                <td>前 6 个 vCPU 免费。它有资格使用 Azure 混合权益</td>
            </tr>
        </table>

        
        <div class="tags-date">
            <div class="ms-date">
                <sup>
                    *
                </sup>
        如果已启用 Arc 的 Kubernetes 群集位于 Azure Stack Edge、Azure Stack HCI 上的 AKS 或 Windows Server 2019 数据中心上的 AKS，那么已附带 Kubernetes 配置且免费提供。有关详细信息，请访问<a target="_blank" href="https://docs.azure.cn/zh-cn/azure-arc/kubernetes/tutorial-use-gitops-connected-cluster">文档页面</a>。</p>
                
    </div>
</div>
<p>其他管理服务(例如适用于 Kubernetes 的 Azure Policy、适用于容器的 Azure Monitor 和 Microsoft Defender for Cloud)以预览版提供，因此目前可免费使用该服务。但是，对于适用于 Kubernetes 的 Azure Policy 和适用于容器的 Azure Monitor，这些服务保留的任何数据都可能会根据该服务的定价收取费用。</p>
<!-- 
        <p>*如果已启用 Arc 的 Kubernetes 群集位于 Azure Stack Edge、Azure Stack HCI 上的 AKS 或 Windows Server 2019 数据中心上的 AKS，那么已附带 Kubernetes 配置且免费提供。有关详细信息，请访问文档页面。</p>

        <p>其他管理服务(例如适用于 Kubernetes 的 Azure Policy、适用于容器的 Azure Monitor 和 Microsoft Defender for Cloud)以预览版提供，因此目前可免费使用该服务。但是，对于适用于 Kubernetes 的 Azure Policy 和适用于容器的 Azure Monitor，这些服务保留的任何数据都可能会根据该服务的定价收取费用。</p> -->

        <h2>已启用 Azure Arc 的服务器</h2>

        <p>启用后，将为已启用 Azure Arc 的服务器收取附加 Azure 管理服务(Azure 更新管理器、Azure Policy 来宾配置、Azure Monitor、Microsoft Defender for Cloud 等)费用。</p>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="/Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="uzp9b-W-yRfCXotmNTxe7gJ6mDvg0MCAMUP0NamHKu1GWRvP6Zn9u_UNIN4bjNDCnkMmUAo87-AUFHnXXoFOA1aWJXsa6GbciCVujc4WtrY1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
  </script>
  <!-- end JSLL -->
  <script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
