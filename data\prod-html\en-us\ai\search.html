<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure AI Search, azure search, cloud search, search service, cognitive search, search as a service, azure search service" name="keywords"/>
    <meta content="Azure AI Search is a fully managed cloud search service that provides a better user experience for customers. Add Cognitive Search to your websites."
          name="description"/>
    <title>
        Azure AI Search - Cloud Search Service - Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/search/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="hide-info" style="display:none;">
    <div class="bg-box">
        <div class="cover-bg">
        </div>
    </div>
    <div class="msg-box">
        <div class="pricing-unavailable-message">
            Not available in the selected region
        </div>
    </div>
</div>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-search" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/search.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/images/service/Icon-web-44-Search-Services.svg"/>
                                <h2>
                                    Azure AI Search
                                    <span>
            Azure AI Search
           </span>
                                </h2>
                                <h4>
                                    Search-as-a-service for web and mobile app development
                                </h4>
                            </div>
                        </div>
                    </div>
                    <h3>
                        Pricing details
                    </h3>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure AI Search
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_storage-blobs">
                                                        Azure AI Search
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="Azure AI Search">
                                                Azure AI Search
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China North 3
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                        China East
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                        China North
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#north-china3" selected="selected" value="north-china3">
                                                China North 3
                                            </option>
                                            <option data-href="#east-china2" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-content">
                            <div class="tab-panel" id="tabContent1">
                                <p>
                                    Offered in combinable units that include reliable storage and throughput, Azure AI Search allows developers to set-up and scale a
                                    search experience quickly and cost-effectively. As the volume of data or throughput needs of an application change, Azure AI Search
                                    can scale out to meet these needs, and then scale back down to reduce costs. To get more performance, a customer can combine units to
                                    gain more queries per second, or a higher document count, or both. Units can also be combined to enable high availability or faster
                                    data ingestion.
                                </p>
                                <p>
                                    High density (HD) mode is an optional setting available for standard S3. High density mode enables customers to pack in a higher
                                    number of indices per Azure AI Search service. This is ideal for customers building multi-tenant SaaS apps that have a large number of
                                    small tenants, trials, or free accounts and want to provide a powerful search experience at a low cost per index.
                                </p>
                                <p>
                                    You can enrich a limited number of documents for free, or attach a billable Cognitive Services resource for larger and more frequent
                                    workloads.
                                    <a href="https://docs.azure.cn/zh-cn/search/cognitive-search-predefined-skills"
                                       style="float: none;display: inline;margin: 0;padding: 0;background-color: white;">
                                        Built-in cognitive skill execution
                                    </a>
                                    is charged at the
                                    <a href="https://www.azure.cn/en-us/pricing/details/cognitive-services/"
                                       style="float: none;display: inline;margin: 0;padding: 0;background-color: white;">
                                        Cognitive Services Pricing Page
                                    </a>
                                    , at the same rate as if you had performed the task directly. Learn more about
                                    <a href="https://docs.azure.cn/zh-cn/search/cognitive-search-attach-cognitive-services"
                                       style="float: none;display: inline;margin: 0;padding: 0;background-color: white;">
                                        skill execution
                                    </a>
                                    .
                                </p>
                                <!-- BEGIN: TAB-CONTROL -->
                                <div class="tab-control-container tab-active">
                                    <h2>
                                        Pricing Details
                                    </h2>
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            *The following prices are tax-inclusive.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            *Monthly pricing estimates are based on 744 hours of usage per month.
                                        </div>
                                    </div>
                                    <table cellpadding="0" cellspacing="0" id="Azure-Cognitive-Search1" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    FREE
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    BASIC
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD S1
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD S2
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD S3
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Storage Optimized L1
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Storage Optimized L2
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Storage
                                            </td>
                                            <td>
                                                50 MB
                                            </td>
                                            <td>
                                                15 GB
                                                (max 45 GB per service)
                                            </td>
                                            <td>
                                                160 GB
                                                (max 1.9 TB per service)
                                            </td>
                                            <td>
                                                512 GB
                                                (max 6 TB per service)
                                            </td>
                                            <td>
                                                1 TB
                                                (max 12 TB per service)
                                            </td>
                                            <td>
                                                2 TB
                                                <br/>
                                                (max 24 TB per service)
                                            </td>
                                            <td>
                                                4 TB
                                                <br/>
                                                (max 48 TB per service)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Max indexes per service
                                            </td>
                                            <td>
                                                3
                                            </td>
                                            <td>
                                                15
                                            </td>
                                            <td>
                                                50
                                            </td>
                                            <td>
                                                200
                                            </td>
                                            <td>
                                                200 or 1000/partition in high density1 mode
                                            </td>
                                            <td>
                                                10
                                            </td>
                                            <td>
                                                10
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Scale out limits
                                            </td>
                                            <td>
                                                N/A
                                            </td>
                                            <td>
                                                Up to 9 units per service
                                                (max 3 partition; max 3 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                                up to 3 partitions in high density mode
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Price per unit
                                            </td>
                                            <td>
                                                Free
                                            </td>
                                            <td>
                                                ￥1.03/hour
                                            </td>
                                            <td>
                                                ￥3.42/hour
                                            </td>
                                            <td>
                                                ￥13.68/hour
                                            </td>
                                            <td>
                                                ￥27.35 /hour
                                            </td>
                                            <td>
                                                ￥39.072/hour
                                            </td>
                                            <td>
                                                ￥78.138/hour
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- <table cellpadding="0" cellspacing="0" id="Azure-Cognitive-Search3" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    FREE
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    BASIC
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD S1
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD S2
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD S3
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Storage
                                            </td>
                                            <td>
                                                50 MB
                                            </td>
                                            <td>
                                                2 GB
                                            </td>
                                            <td>
                                                25 GB
                                                (max 300 GB per service)
                                            </td>
                                            <td>
                                                100 GB
                                                (max 1 TB per service)
                                            </td>
                                            <td>
                                                200 GB
                                                (max 2 TB per service)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Max indexes per service
                                            </td>
                                            <td>
                                                3
                                            </td>
                                            <td>
                                                15
                                            </td>
                                            <td>
                                                50
                                            </td>
                                            <td>
                                                200
                                            </td>
                                            <td>
                                                200 or 1000/partition in high density1 mode
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Scale out limits
                                            </td>
                                            <td>
                                                N/A
                                            </td>
                                            <td>
                                                Up to 3 units per service
                                                (max 1 partition; max 3 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                            </td>
                                            <td>
                                                Up to 36 units per service
                                                (max 12 partition; max 12 replicas)
                                                up to 12 replicas in high density1 mode
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Price per unit
                                            </td>
                                            <td>
                                                Free
                                            </td>
                                            <td>
                                                ￥1.03/hour
                                            </td>
                                            <td>
                                                ￥3.42/hour
                                            </td>
                                            <td>
                                                ￥13.68/hour
                                            </td>
                                            <td>
                                                ￥27.35 /hour
                                            </td>
                                        </tr>
                                    </table> -->
                                    <p>
                                        
                                        <span class="tags-date">
                                            <span class="ms-date">
                                                <sup>
                                                    1
                                                </sup>
                                                High density (HD) mode is an option available within the standard S3 service that allows a larger number of indexes to be created
                                                in a single service. Using HD mode, a service can create up to 1,000 indexes/partition, where an index may be no more than 1
                                                million documents (or 2 GB storage), and the total number of documents and storage across all indexes may not exceed 120 million
                                                documents/partition or 200 GB/partition.
                                                Learn more about
                                                <a href="https://docs.azure.cn/zh-cn/search/search-limits-quotas-capacity"
                                                style="float: none;display: inline;margin: 0;padding: 0;background-color: white;font-size: 12px;">
                                                    service limits and constraints
                                                </a>
                                            </span>
                                        </span>
                                        
                                    </p>
                                </div>
                                <!-- <div class="tab-control-container tab-active">
                                                      <h3>Cognitive Search: AI-Powered Content Augmentation</h3>
                                                      <p>With the new cognitive search feature, you can use artificial intelligence to extract insights and structured information from your documents. Create pipelines that uses cognitive skills to enrich and bring structure to your data before it gets indexed. You can select from a variety of pre-built cognitive skills and also extend its power by creating your own custom skills. You can enrich a limited number of documents for free, or attach a billable Cognitive Services resource for larger and more frequent workloads. <a style="float: none;display: inline;margin: 0;padding: 0;background-color: white;" href="https://docs.azure.cn/zh-cn/search/cognitive-search-predefined-skills">Built-in cognitive skill execution</a> is charged at the <a  style="float: none;display: inline;margin: 0;padding: 0;background-color: white;" href="https://www.azure.cn/en-us/pricing/details/cognitive-services/">Cognitive Services Pricing Page</a>, at the same rate as if you had performed the task directly. Learn more about <a style="float: none;display: inline;margin: 0;padding: 0;background-color: white;" href="https://docs.azure.cn/zh-cn/search/cognitive-search-attach-cognitive-services">skill execution</a>.</p>
                                                  </div>
                                                  <div class="tab-control-container tab-active">
                                                      <h3>Cognitive Search: Custom Entity Lookup cognitive skill</h3>
                                                      <p>The Custom Entity Lookup skill looks for text from a custom, user-defined list of words and phrases. It could label all documents with any matching entities. The skill also supports a degree of fuzzy matching that can be applied to find matches that are similar but not quite exact. There are 3 different ways to provide the list of custom entities to the Custom Entity Lookup skill. You can provide the list in a .CSV file, a .JSON file or as an inline definition as part of the skill definition.</p>
                                                      <table cellpadding="0" cellspacing="0" width="100%" id="Azure-Cognitive-Search2">
                                                          <tr>
                                                              <th align="left"><strong></strong></th>
                                                              <th align="left"><strong>Price</strong></th>
                                                          </tr>
                                                          <tr>
                                                              <td>0-500K</td>
                                                              <td>￥10.176/1K Text Records</td>
                                                          </tr>
                                                          <tr>
                                                              <td>500K-2500K</td>
                                                              <td>￥7.632/1K Text Records</td>
                                                          </tr>
                                                          <tr>
                                                              <td>2500K-10000K</td>
                                                              <td>￥3.0528/1K Text Records</td>
                                                          </tr>
                                                          <tr>
                                                              <td>￥3.0528/1K Text Records</td>
                                                              <td>￥2.544/1K Text Records</td>
                                                          </tr>
                                                      </table>
                                                      <div class="tags-date">
                                                          <div class="ms-date">Note： Billing above will be officially  effective starting on May 1, 2021. </div>
                                                      </div>
                                                  </div> -->
                                <div class="tab-control-container tab-active">
                                    <h3>
                                        Additional Azure AI Search Features (Billed Separately)
                                    </h3>
                                    <table cellpadding="0" cellspacing="0" id="Azure-Cognitive-Search2" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    What This Feature Does
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Pricing Details
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                <a href="https://learn.microsoft.com/en-us/azure/search/cognitive-search-skill-custom-entity-lookup"
                                                   style="background-color: transparent;color: #006fc3;">
                                                    Custom Entity Lookup Skill
                                                </a>
                                            </td>
                                            <td>
                                                Looks for text from a custom, user-defined list of words and phrases and labels
                                                <br/>
                                                all documents containing matching entities. Available for all Basic, Standard,
                                                <br/>
                                                and Storage-Optimized tiers.
                                                <br/>
                                                When to use: you want to define and detect specific entities in your data.
                                            </td>
                                            <td>
                                                0-1M text records ￥10.176 per 1,000 text records
                                                <br/>
                                                1M-3M text records ￥7.632 per 1,000 text records
                                                <br/>
                                                3M-10M text records ￥3.0528 per 1,000 text records
                                                <br/>
                                                10M+ text records ￥2.544 per 1,000 text records
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <a href="https://learn.microsoft.com/en-us/azure/search/cognitive-search-skill-document-extraction?msclkid=bbca0a62b1d111ecbe4ba5b40c5c056d"
                                                   style="background-color: transparent;color: #006fc3;">
                                                   Document Cracking: Image Extraction
                                                </a>
                                            </td>
                                            <td>
                                                Extracts content from a file within the enrichment pipeline. Text extraction is free.
                                                <br/>
                                                Image extraction is billed during the initial document cracking step and when invoking the Document Extraction skill.
                                                <br/>
                                                When to use: you have documents that contain images.
                                            </td>
                                            <td>
                                                0-1M images ￥10.18 per 1,000 transactions
                                                <br/>
                                                1M-5M images ￥8.14 per 1,000 transactions
                                                <br/>
                                                5M+ images ￥6.61 per 1,000 transactions
                                            </td>
                                        </tr>

                                        <tr>
                                            <td>
                                                <a href="https://learn.microsoft.com/en-us/azure/search/semantic-search-overview"
                                                   style="background-color: transparent;color: #006fc3;">
                                                   Semantic ranker
                                                </a>
                                            </td>
                                            <td>
                                                Uses AI models to improve the relevance of the search results by finding content that is semantically similar to query terms. The service is only available for accounts on Basic, Standard tiers (S1, S2, and S3), and Storage-Optimized (L1 and L2) and has two pricing plans within those tiers.
                                                <br/>
                                                When to use: you want to improve the quality of search results and optimize the user experience.
                                            </td>
                                            <td>
                                                First 1,000 requests per month free. ￥10 per 1,000 additional requests
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- <div class="tags-date">
                                                             <div class="ms-date"><sup>1</sup>The standard pricing plan for semantic search is billed daily and can be turned off early without incurring the full monthly charge </div>
                                                         </div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-1">
                                            How does the usage of a standard unit show up on my bill?
                                        </a>
                                        <section>
                                            <p>
                                                With Azure AI Search, you are billed on a flat, predictable hourly rate based on the number of units that have been used
                                                during any given hour.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-2">
                                            If I scale up or scale down my service, what will I be charged?
                                        </a>
                                        <section>
                                            <p>
                                                You are billed the maximum number of search units provisioned during any hour. If you start with two units, then scale to
                                                four units, and then scale back down to two units all within an hour, you will be charged for four units.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-3">
                                            If I click the “stop” button in the Azure portal, will I still be charged?
                                        </a>
                                        <section>
                                            <p>
                                                The stop button is meant to stop traffic to your service instance. As a result, your service is still running and will
                                                continue to be charged the hourly rate.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-4">
                                            What if my unit is active for less than an hour?
                                        </a>
                                        <section>
                                            <p>
                                                You are billed the flat rate for each hour the unit exists, regardless of usage or if the unit is active for less than an
                                                hour. For example, if you create a unit and delete it five minutes later, your bill will reflect a charge for one unit
                                                hour.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-5">
                                            What if I need more throughput or more storage?
                                        </a>
                                        <section>
                                            <p>
                                                Azure AI Search units combine to provide additional throughput and storage. For example, to scale from 15 million
                                                documents to 30 million (additional partitions), a customer can purchase two units. To increase throughput (additional
                                                replicas), they can purchase two units. To increase both storage and throughput, a customer would need to purchase four
                                                units (2 replicas x 2 partitions = 4 search units).
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-6">
                                            What are the differences among the editions offered by Azure AI Search?
                                        </a>
                                        <section>
                                            <p>
                                                Free is a free version of Azure AI Search designed to provide developers a sandbox to test features and implementations of
                                                Azure AI Search. It is not designed for production workloads. Basic, standard, and storage optimized are the go-to options
                                                for building applications that benefit from a self-managed search-as-a-service solution. Standard delivers storage and
                                                predictable throughput that scales with application needs.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="search-question-7">
                                            How do I create an Azure AI Search account?
                                        </a>
                                        <section>
                                            <p>
                                                Azure AI Search is available in the new
                                                <a href="https://portal.azure.cn/">
                                                    Azure Portal
                                                </a>
                                                . First, you must sign up for an Azure subscription, then you can add an Azure AI Search account to your Azure
                                                subscription via the gallery in the preview portal.
                                                <a href="https://docs.azure.cn/zh-cn/search/search-manage">
                                                    Get more information
                                                </a>
                                                .
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="search-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee that apps running in a customer subscription will be available 99.95% of the time. No Service Level Agreement is offered for the
                            Free or Shared tiers. To learn more about the details of our Service Level Agreement, please visit the , please visit the
                            <a href="/en-us/support/sla/search/v1_0/" id="pricing_search_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="7SVamHoZZJlJO-auu0KIv6KQz1OeNKChwNkYB1cjZQgQzThZt2sGQ3jAL7b8A6GaS_A90yHRLWoA9mlRb5hQCxJSls7VSszUlBVnVzZi18A1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- <script src='/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js' type='text/javascript'></script> -->
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
