<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Microsoft Cloud, networking, Virtual Networking, Traffic Manager, Traffic Manager, load balancing, traffic" name="keywords"/>
    <meta content="Learn about the pricing details of Azure Traffic Manager. Azure Traffic Manager is a cloud-based load balancing service that improves application performance by automatically load balancing incoming traffic across data centers. Traffic Manager uses more favorable, step-wise pricing. A 1RMB Trial gets you ￥1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer to enjoy a Service Level Agreement of up to 99.99%."
          name="description"/>
    <title>
        Azure Traffic Manager Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/traffic-manager/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-traffic-manager" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/traffic_manager.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Traffic Manager
                                </h2>
                                <h4>
                                    Route incoming traffic for high performance and availability
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Applying the Azure Traffic Manager, you can make the incoming traffic among several hosted Azure services achieve
                            load balance, whether these services are operated in the same data center or different data centers in China.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <h2>
                                Pricing Details
                            </h2>
                            <!-- <p>从2016年4月1日起，价格会下调 25.5%，以下是下调后的新价格：</p> -->
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Pricing
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        First 1 billion DNS queries/month
                                    </td>
                                    <td>
                                        ￥3.57/1 million queries
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Over 1 billion DNS queries/month
                                    </td>
                                    <td>
                                        ￥1.79/1 million queries
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Health checks (Azure)
                                    </td>
                                    <td>
                                        ￥2.38 per Azure endpoint/month
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Fast interval health checks add-on (Azure)
                                        <sup>
                                            1
                                        </sup>
                                    </td>
                                    <td>
                                        ￥8.16 per Azure endpoint/month
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Health checks (external)
                                    </td>
                                    <td>
                                        ￥3.57 per external endpoint/month
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Fast interval health checks add-on (external)
                                        <sup>
                                            1
                                        </sup>
                                    </td>
                                    <td>
                                        ￥15.24 per Azure endpoint/month
                                    </td>
                                </tr>
                            </table>
                            <p>
                                <sup>
                                    1
                                </sup>
                                Fast endpoint health checks need to be purchased as an add-on to basic endpoint health checks.
                            </p>
                            <!-- END: Table1-Content-->
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                    </div>
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Traffic_Manager_question1">
                                            What is a Traffic Manager “DNS query”?
                                        </a>
                                        <section>
                                            <p>
                                                Whenever users access your applications, your service name is mapped to the IP address with the DNS query. By
                                                providing different responses to different DNS queries, Traffic Manager can make the incoming traffic of your
                                                several hosted Azure services achieve load balance, no matter whether these services are operated in the same
                                                data center or different data centers in China.
                                            </p>
                                            <p>
                                                Traffic Manager provides several load-balance methods, including performance, failover and round robin. Applying
                                                these methods can effectively manage traffic, ensure higher performance, availability and recovery capability.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Traffic_Manager_question2">
                                            What is Traffic Manager “Health Check”?
                                        </a>
                                        <section>
                                            <p>
                                                Applying Traffic Manager, you can utilize Azure service endpoints for monitoring and hosting, and Traffic Manager
                                                can also supply automatic failover when a service goes down, so as to improve availability of critical
                                                applications.
                                            </p>
                                            <p>
                                                Therefore, Traffic Manager will continuously monitor the health of each service endpoint. When these “Health
                                                Checks” detect that a service has goes down, Traffic Manager re-routes traffic to the other services.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Traffic_Manager_question3">
                                            Are all pricing of the load-balancing methods the same?
                                        </a>
                                        <section>
                                            <p>
                                                Yes. No matter which load-balance method is used, pricing for DNS query and health checks are the same.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="Traffic_Manager_question4">
                                            How to reduce Traffic Manager bill?
                                        </a>
                                        <section>
                                            <p>
                                                The PC of the end user will not directly contact the name server of the traffic manager while performing a DNS
                                                query. Instead, these queries will be sent by the "recursive" DNS server run by the enterprise and the ISP.
                                                These servers cache the DNS responses, so that other users' queries can be processed quickly. Meanwhile, these
                                                cached responses will not reach the Traffic Manager name server, thus no cost is incurred.
                                            </p>
                                            <p>
                                                The cache duration is determined by the “TTL” parameters in the original DNS response. This parameter is
                                                configurable in Traffic Manager with a default value of 300s and minimal value of 30s.
                                            </p>
                                            <p>
                                                The larger TTL can be applied to increase the quantity of the completed cache of the recursive DNS server, so as
                                                to decrease DNS query fees. However, increasing the cache will also affect the speed for the end user receiving
                                                status changes in endpoint (that is, in case of failure of an endpoint, the failover time of your end user will
                                                be extended). Thus we do not recommend using extremely large TTL values.
                                            </p>
                                            <p>
                                                Likewise, if the TTL is quite small, the failover speed will become faster, while due to the decreased cache, the
                                                query times for the name server of the Traffic Manager will increase.
                                            </p>
                                            <p>
                                                By configuring the TTL value by yourself, Traffic Manager allows you to set the optimal TTL value based on the
                                                business needs of the application.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <div class="pricing-page-section">
                             <h2>上市地区</h2>
                             <p>流量管理器在以下区域中提供：</p>
                             <table cellpadding="0" cellspacing="0" class="table-col6">
                                 <tr>
                                     <th align="left"><strong>地域</strong></th>
                                     <th align="left"><strong>区域</strong></th>
                                 </tr>
                                 <tr>
                                     <td>中国大陆</td>
                                     <td>中国东部数据中心 , 中国北部数据中心</td>
                                 </tr>
                             </table> -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="traffic-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee that DNS queries will receive valid responses from at least one of our Azure Traffic Manager Name Clusters
                            at least 99.99% of the time. Availability is calculated by the monthly billing cycle. To learn more about the details of
                            our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/traffic-manager/index.html" id="pricing_traffic_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="6TyOKPB8Ych-Wp1GN8rKPRkr_Hlgo6kInFbsIE7u0P-7XfWs1YB674BlkAj0hvdFoEHvbUGhaCizVMojKbBYiFahn1an_Ft-ORHbmlhTxrQ1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
