#!/usr/bin/env python3
"""
测试HTML文件自动复制脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.auto_copy_html import HTMLFileCopier

def test_single_category():
    """测试单个分组的复制"""
    print("测试单个分组复制...")

    copier = HTMLFileCopier(str(project_root))

    # 测试database分组，只处理中文版本
    result = copier.run("zh-cn", ["database"])

    print(f"测试结果: 成功 {result['total_success']}, 失败 {result['total_fail']}")

    # 检查生成的文件
    target_dir = project_root / "data" / "prod-html" / "zh-cn" / "database"
    if target_dir.exists():
        files = list(target_dir.glob("*.html"))
        print(f"生成的文件: {[f.name for f in files]}")
    else:
        print("目标目录不存在")

def test_file_finding():
    """测试文件查找功能"""
    print("\n测试文件查找功能...")

    copier = HTMLFileCopier(str(project_root))

    # 测试几个产品的文件查找
    test_products = [
        "sql-database",
        "storage-files",
        "anomaly-detector",
        "ip-address",
        "core-control-plane"
    ]

    for product in test_products:
        zh_file = copier.find_html_file(product, "zh-cn")
        en_file = copier.find_html_file(product, "en-us")

        print(f"{product}:")
        print(f"  zh-cn: {'✓' if zh_file else '✗'} {zh_file if zh_file else 'Not found'}")
        print(f"  en-us: {'✓' if en_file else '✗'} {en_file if en_file else 'Not found'}")

if __name__ == "__main__":
    print("开始测试HTML文件自动复制脚本...")

    test_file_finding()
    test_single_category()

    print("\n测试完成!")