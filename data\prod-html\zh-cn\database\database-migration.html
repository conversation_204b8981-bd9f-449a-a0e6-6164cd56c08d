<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="数据库迁移, microsoft azure, azure" name="keywords"/>
  <meta content="Azure 数据库迁移服务使用功能全面而操作简单的自我引导式迁移流程，将 SQL Server 和 Oracle 数据库轻松、安全地迁移到云，从而降低云迁移的复杂度。" name="description"/>
  <title>
   定价 - Azure 数据库迁移服务 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/database-migration/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="database-migration" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/database-migration_banner.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           Azure 数据库迁移服务
           <span>
            Azure Database Migration Service
           </span>
          </h2>
          <h4>
           加速转换到云
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure 数据库迁移服务 (DMS) 是一项完全托管的服务，用于迁移操作数据库和数据仓库。
        </p>
        <p>
         标准定价层支持脱机（又称为“一次性”）迁移。标准定价层提供 1-vCore、2-vCore 和 4-vCore 选项，已正式面向客户免费推出。
        </p>
        <p>
         针对要求停机时间最短的业务关键型工作负载，高级定价层支持脱机和联机迁移（又称为“持续迁移”）。现已正式推出高级定价层。
        </p>
        <h2>
         定价详细信息
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Database Migration
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_database-migration">
                Database Migration
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" value="Database Migration">
              Database Migration
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: Level 1 tab content panel 1 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 1 -->
          <div class="category-container-container">
           <div class="category-container-box">
            <div class="category-container">
             <span class="category-title hidden-lg hidden-md">
              类别：
             </span>
             <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
              <li class="active">
               <a data-href="#tabContent1-0" href="javascript:void(0)" id="database-migration-all">
                全部
               </a>
              </li>
              <li>
               <a data-href="#tabContent1-1" href="javascript:void(0)" id="database-migration-Standard">
                标准
               </a>
              </li>
              <li>
               <a data-href="#tabContent1-2" href="javascript:void(0)" id="database-migration-Premium">
                高级
               </a>
              </li>
             </ul>
             <select class="dropdown-select category-tabs hidden-lg hidden-md">
              <option data-href="#tabContent1-0" id="database-migration-all" value="all">
               全部
              </option>
              <option data-href="#tabContent1-1" id="database-migration-Standard" value="Standard">
               高级
              </option>
              <option data-href="#tabContent1-2" id="database-migration-Premium" value="Premium">
               高级
              </option>
             </select>
            </div>
           </div>
          </div>
          <!-- END: Tab level 2 navigator 1 -->
          <!-- BEGIN: Tab level 2 content -->
          <div class="tab-content">
           <!-- BEGIN: Tab level 2 content 1-1 -->
           <div class="tab-panel" id="tabContent1-1">
            <h3>
             标准
            </h3>
            <p>
             大多数中小企业工作负载（仅支持脱机迁移）
            </p>
            <div class="scroll-table" style="display:block;">
             <h3>
              计算
             </h3>
             <p>
              在虚拟核心 (vCore) 中预配计算。vCore 表示逻辑 CPU。
             </p>
             <div class="tags-date">
              <div class="ms-date">
               *以下价格均为含税价格。
              </div>
              <br/>
              <div class="ms-date">
               *每月价格估算基于每个月 31 天的使用量。
              </div>
              <br/>
             </div>
             <table cellpadding="0" cellspacing="0" id="database-migration-standard-compute" width="100%">
              <tr>
               <th align="left">
                <strong>
                 vCore
                </strong>
               </th>
               <th align="left">
                <strong>
                 价格
                 <sup>
                  *
                 </sup>
                </strong>
               </th>
              </tr>
              <tr>
               <td>
                1
               </td>
               <td>
                免费
               </td>
              </tr>
              <tr>
               <td>
                2
               </td>
               <td>
                免费
               </td>
              </tr>
              <tr>
               <td>
                4
               </td>
               <td>
                免费
               </td>
              </tr>
             </table>
            </div>
           </div>
           <div class="tab-panel" id="tabContent1-2">
            <div>
             <h3>
              高级
             </h3>
             <p>
              大型或业务关键型工作负载（支持联机迁移、脱机迁移和较快的迁移速度）
             </p>
             <div class="scroll-table" style="display:block;">
              <h3>
               计算
              </h3>
              <p>
               在虚拟核心 (vCore) 中预配计算。vCore 表示逻辑 CPU。
              </p>
              <div class="tags-date">
               <div class="ms-date">
                *以下价格均为含税价格。
               </div>
               <br/>
               <div class="ms-date">
                *每月价格估算基于每个月 31 天的使用量。
               </div>
              </div>
              <table cellpadding="0" cellspacing="0" id="database-migration-premium-compute" width="100%">
               <tr>
                <th align="left">
                 <strong>
                  VCore
                 </strong>
                </th>
                <th align="left">
                 <strong>
                  价格
                 </strong>
                </th>
               </tr>
               <tr>
                <td>
                 4
                </td>
                <td>
                 ￥ 1.9/小时
                 <br/>
                 (约￥ 1,413.6/月)
                </td>
               </tr>
              </table>
              <div class="tags-date">
               <div class="ms-date">
                <sup>
                 *
                </sup>
                自 DMS 服务创建之日起可免费使用 DMS 高级 4-vCore 六个月（183 天），之后将产生费用。
               </div>
              </div>
             </div>
             <!-- END: Table1-Content-->
            </div>
           </div>
          </div>
         </div>
         <!-- END: TAB-CONTAINER-1 -->
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <h3>
          综合
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_database-migration_How is my bill calculated for the Standard tier of Database Migration Service">
             如何针对数据库迁移服务标准层计算我的账单？
            </a>
            <section>
             <p>
              数据库迁移服务的标准层支持脱机迁移且免费提供。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_database-migration_How is my bill calculated for the Premium tier of Database Migration Service">
             如何针对数据库迁移服务高级层计算我的账单？
            </a>
            <section>
             <p>
              DMS 高级层根据 vCore 中针对你的迁移服务预配的计算，按可预测的每小时费率进行计费。
             </p>
             <p>
              你可自 DMS 服务创建之日起免费使用 4 vCore 高级 DMS 六个月（183 天），之后开始计费。
             </p>
             <p>
              例如：
             </p>
             <ul>
              <li>
               如果自 DMS 服务创建之日起你使用 4 vCore 高级 DMS 的时间不超过 6 个月（183 天），则不对你计费（DMS 高级 4 vCore 自 DMS 创建起免费提供 183 天）。因此，如果你使用了 4 vCore 高级 DMS 三个月，则该服务免费。
              </li>
              <li>
               如果你在 3 月 15 日创建 4 vCore 高级 DMS 服务，则你的 6 个月（183 天）免费期将于服务创建之日开始启动，持续到 9 月 14 日。对于 9 月 15 日或之后产生的使用量，将对你预配的计算资源按小时计费。如果使用 4 vCore 高级 DMS 的时间是 3 月 15 日至 11 月 1 日，则你在 9 月 14 日（183 天）之前可免费使用，然后从 9 月 15 至 11 月 1 日就你的使用量向你收费。
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_database-migration_How long can I use the Premium tier of Database Migration Service">
             我可使用数据库迁移服务高级层多长时间？
            </a>
            <section>
             <p>
              创建 DMS 服务后，可自服务创建之日起使用 DMS 长达 1 年。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_database-migration_How many DMS services can I create">
             我可创建多少个 DMS 服务？
            </a>
            <section>
             <p>
              客户可对每个订阅创建 2 个 DMS 服务。要另外创建服务，请创建支持票证。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="database-migration-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/legal/sla" id="pricing_database-migration_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="JVV_OlLlpvRj8UkZPEpa_wfHtzoGmMPmjIYdKKmsxJuw1U92pQdMfBrBPoj52-9xRrlDQQzDRcpX1D1_hKAkEs5_52XHuVamhs3EIJp418o1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
