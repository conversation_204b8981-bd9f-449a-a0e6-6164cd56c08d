<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="查看AzureAzure AI 搜索(一种面向web和应用程序开发人员的云搜索即服务)的详细定价。没有前期费用。按量付费。免费使试用。" name="description"/>
  <meta content="azure 搜索, 云平台, 云搜索, 微软 azure,azure" name="keywords"/>
  <title>
   价格 - Azure AI 搜索 | 微软搜索 Azure
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/zh-cn/pricing/details/cognitive-search/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
  </style>
  <div class="hide-info" style="display:none;">
   <div class="bg-box">
    <div class="cover-bg">
    </div>
   </div>
   <div class="msg-box">
    <div class="pricing-unavailable-message">
     所选区域不可用
    </div>
   </div>
  </div>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="search" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_app_service-01.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/images/service/Icon-web-44-Search-Services.svg"/>
          <h2>
           Azure AI 搜索
           <span>
            Azure AI Search
           </span>
          </h2>
          <h4>
           用于 Web 和移动应用程序开发的搜索即服务
          </h4>
         </div>
        </div>
       </div>
       <h2>
        Azure AI 搜索定价
       </h2>
       <!-- END: Product-Detail-TopBanner -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure AI Search
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_storage-blobs">
                Azure AI Search
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Azure AI Search">
              Azure AI Search
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#north-china3" selected="selected" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <div class="tab-panel" id="tabContent1">
          <p>
           Azure Azure AI 搜索以组合单位的形式提供，包括可靠的存储和吞吐量，可让开发人员快速且经济高效地建立和扩展搜索体验。随着应用程序的数据量或吞吐量需求不断变化，Azure
                                    Azure AI 搜索可以先扩大以满足这些需求，然后重新减少以降低成本。要获取更高性能，客户可以组合这些单位以获得更多的每秒查询数和/或更高的文档计数。还可组合单位以提供高可用性或更快的数据采集。
          </p>
          <p>
           高密度 (HD) 模式是一个可用于标准 S3 的可选设置。通过高密度模式，客户可在每次 Azure
                                    Azure AI 搜索服务时输入更多数量的索引。如何客户拥有大量小租户、试用版或免费帐户，并希望按单次索引低成本的方式提供强大的搜索体验，则这是他们构建多租户
                                    SaaS 应用时的理想选择。
          </p>
          <p>
           <strong>
            可以免费扩充有限数量的文档，或者附加付费的认知服务资源来处理更大、更频繁的工作负载。
           </strong>
           <a href="https://docs.azure.cn/zh-cn/search/cognitive-search-predefined-skills" id="pricing_logic-apps_sla" style="float: none;display: inline;margin: 0;padding: 0;background-color: white;">
            内置认知技能执行
           </a>
           <strong>
            按
           </strong>
           <a href="https://www.azure.cn/pricing/details/cognitive-services/index.html" id="pricing_logic-apps_sla" style="float: none;display: inline;margin: 0;padding: 0;background-color: white;">
            认知服务价格详情
           </a>
           收费，其费率与你以往直接执行任务的费率相同。详细了解
           <a href="https://docs.azure.cn/zh-cn/search/cognitive-search-attach-cognitive-services" id="pricing_logic-apps_sla" style="float: none;display: inline;margin: 0;padding: 0;background-color: white;">
            技能执行
           </a>
           。
          </p>
          <div class="tags-date">
           <div class="ms-date">
           </div>
           <br/>
           <!-- <div class="ms-date">*以下价格表中的新价格及基本定价层将于 2018 年 3 月 1 日正式生效。 </div> -->
          </div>
          <table cellpadding="0" cellspacing="0" id="Azure-Cognitive-Search1" width="100%">
           <tr>
            <th align="left">
             <strong>
             </strong>
            </th>
            <th align="left">
             <strong>
              免费
             </strong>
            </th>
            <th align="left">
             <strong>
              基本
             </strong>
            </th>
            <th align="left">
             <strong>
              标准版S1
             </strong>
            </th>
            <th align="left">
             <strong>
              标准版S2
             </strong>
            </th>
            <th align="left">
             <strong>
              标准版S3
             </strong>
            </th>
            <th align="left">
             <strong>
              存储优化版 L1
             </strong>
            </th>
            <th align="left">
             <strong>
              存储优化版 L2
             </strong>
            </th>
           </tr>
           <tr>
            <td>
             存储
            </td>
            <td>
             50MB
            </td>
            <td>
              15GB<br/>（每服务最多 45 GB）
            </td>
            <td>
             160GB<br/>（每服务最多 1.9 TB）
            </td>
            <td>
             512 GB <br/>（每服务最多 6 TB）
            </td>
            <td>
             1 TB <br/>（每服务最多 12 TB）
            </td>
            <td>
             2 TB
             <br/>
             (每服务最多 24 TB)
            </td>
            <td>
             4 TB
             <br/>
             (每服务最多 48 TB)
            </td>
           </tr>
           <tr>
            <td>
             最大索引数/服务
            </td>
            <td>
             3
            </td>
            <td>
             15
            </td>
            <td>
             50
            </td>
            <td>
             200
            </td>
            <td>
             200 或 1000/分区
             <br/>
             （按高密度
             <sup>
              1
             </sup>
             模式）
            </td>
            <td>
             10
            </td>
            <td>
             10
            </td>
           </tr>
           <tr>
            <td>
             横向扩展限制
            </td>
            <td>
                N/A
            </td>
            <td>
             最多可达 9 个单位/服务
             <br/>
             （最多 3 个分区；
             <br/>
             最多 3 个副本）
            </td>
            <td>
             最多可达 36 个单位/服务
             <br/>
             （最多 12 个分区；
             <br/>
             最多 12 个副本）
            </td>
            <td>
             最多可达 36 个单位/服务
             <br/>
             （最多 12 个分区；
             <br/>
             最多 12 个副本）
            </td>
            <td>
             最多可达 36 个单位/服务
             <br/>
             （最多 12 个分区；
             <br/>
             最多 12 个副本）
             <br/>
             最多 3 个高密度模式下的分区
            </td>
            <td>
             最多可达 36 个单位/服务
             <br/>
             （最多 12 个分区；最多 12 个副本）
            </td>
            <td>
             最多可达 36 个单位/服务
             <br/>
             （最多 12 个分区；最多 12 个副本）
            </td>
           </tr>
           <tr>
            <td>
             价格/单位
            </td>
            <td>
             免费
            </td>
            <td>
             ￥1.03/小时
            </td>
            <td>
             ￥3.42/小时
            </td>
            <td>
             ￥13.68/小时
            </td>
            <td>
             ￥27.35 /小时
            </td>
            <td>
             ￥39.072/小时
            </td>
            <td>
             ￥78.138/小时
            </td>
           </tr>
          </table>
        
          <p>
           
                                   <span class="tags-date" style="line-height: 5px;">
                                    <span class="ms-date">
                                        <sup>
                                            1
                                           </sup>
                                           高密度 (HD) 模式是一个可在标准 S3 服务中使用的选项，允许在单个服务中创建更多数量的索引。 借助 HD 模式，服务可创建多达 1,000
                                                                    个索引，其中一个索引不得超过 1 百万个文档（即 2 GB 的存储空间），且所有索引的文档和存储空间总数不得超过 1.2 亿个文档/分区（即 200 GB/分区）。
                                        了解有关
                                        <a href="https://docs.azure.cn/zh-cn/search/search-limits-quotas-capacity" style="float: none;display: inline;margin: 0;padding: 0;background-color: white; color: #006fc3;font-size:12px;">
                                         服务限制和约束
                                        </a>
                                        的更多信息
                                       </span>
                                   </span>
          </p>
          
          <div class="tab-panel" id="tabContent2">
           <h2>
            其他 Azure AI 搜索功能(单独计费)
           </h2>
           <table cellpadding="0" cellspacing="0" id="Azure-Cognitive-Search2" width="100%">
            <tr>
             <th align="left">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               此功能的作用
              </strong>
             </th>
             <th align="left">
              <strong>
               定价详细信息
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              <a href="https://learn.microsoft.com/zh-cn/azure/search/cognitive-search-skill-custom-entity-lookup" style="background-color: transparent;color: #006fc3;">
               自定义实体查找技能
              </a>
             </td>
             <td>
              从用户定义的自定义字词和短语列表查找文本，并标记所有包含匹配实体的文档。
              <br/>
              适用于所有基本、标准和存储优化层。
              <br/>
              使用时间: 要在数据中定义并检测指定实体。
             </td>
             <td>
              0-1M 的文本记录 ￥10.176 每 1,000 条文本记录
              <br/>
              1M-3M 的文本记录 ￥7.632 每 1,000 条文本记录
              <br/>
              3M-10M 的文本记录 ￥3.0528 每 1,000 条文本记录
              <br/>
              10M 以上的文本记录 ￥2.544 每 1,000 条文本记录
              <br/>
             </td>
            </tr>
            <tr>
             <td>
              <a href="https://learn.microsoft.com/zh-cn/azure/search/cognitive-search-skill-document-extraction?msclkid=bbca0a62b1d111ecbe4ba5b40c5c056d" style="background-color: transparent;color: #006fc3;">
               文档破解：图像提取
              </a>
             </td>
             <td>
              从扩充管道中的文件提取内容。文本提取免费。
              <br/>
              在执行初始文档破解步骤期间和调用“文档提取”技能时，对图像提取进行计费。
              <br/>
              使用时间: 具有包含图像的文档。
             </td>
             <td>
              0-1M 的图像 ￥10.18每 1,000 项事务
              <br/>
              1M-5M 的图像 ￥8.14每 1,000 项事务
              <br/>
              5M 以上的图像 ￥6.61 每 1,000 项事务
             </td>
            </tr>

            <tr>
             <td>
              <a href="https://learn.microsoft.com/zh-cn/azure/search/semantic-search-overview" style="background-color: transparent;color: #006fc3;">
               语义排名排序
              </a>
             </td>
             <td>
                使用 AI 模型，通过查找语义上类似于查询字词的内容，提高搜索结果的相关性。该服务仅适用于基本层、标准层(S1、S2 和 S3)和存储空间优化层(L1 和 L2)上的帐户，且在这些层中具有两个定价计划。
              <br/>
                何时使用: 要改进搜索结果的质量并优化用户体验。
             </td>
             <td>
                每月前 1,000 个请求免费。 每 1000 个额外请求￥10
             </td>
            </tr>
           </table>
          </div>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_region">
             我的账单上以什么形式反映标准单位使用量的收费？
            </a>
            <section>
             <p>
              对于 Microsoft Azure AI 搜索，我们基于任何给定小时使用的单位数量提供统一的可预测小时费率对你收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_question1">
             如果我的服务纵向扩展或纵向缩减，将怎样收费？
            </a>
            <section>
             <p>
              将按任意小时中所设置的最大搜索单位数进行计费。如果你在一个小时内以两个单位开始，然后扩大到四个单位，随后再减少到两个单位，那么将按四个单位向你收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_question2">
             如果我在Azure门户中单机“停止”按钮，是否仍会向我收费？
            </a>
            <section>
             <p>
              停止按钮用于停止服务实例的流量。结果是，你的服务仍将运行，并且会继续按小时费率收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_question2">
             如果我的单位存在时间不足一个小时如何计费？
            </a>
            <section>
             <p>
              将按单位存在的每小时的统一费率对你收费，无论使用量是多少，也无论单位存在时间是否不足一个小时。例如，如果你创建一个单位，然后在五分钟后删除它，那么你的帐单将反映一个单位小时的收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_question2">
             如果需要更多吞吐量或更多存储该怎么办？
            </a>
            <section>
             <p>
              Azure Azure AI 搜索单位组合提供额外吞吐量和存储。例如，要从 1,500 万份文档扩展到 3,000
                                                万（额外分区），客户可以购买两个单位。要增加吞吐量（额外副本），他们可以购买两个单位。要同时增加存储和吞吐量，客户将需要购买四个单位（2
                                                个副本 x 2 个分区 = 4 个搜索单位）。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_question2">
             Azure AI 搜索提供的版本之间有什么差别？
            </a>
            <section>
             <p>
              “免费版”是 Azure AI 搜索的免费版本，设计用于为开发者提供沙盒以测试功能和实施 Azure
                                                Azure AI 搜索。该版本不设计用于生产工作负载。“基本版”和“标准版”是生成从自主管理的搜索即服务解决方案中受益的应用程序的入门选项。Standard
                                                提供随着应用程序需要扩展的存储和可预测吞吐量。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Data_Transfer_question2">
             如何创建Azure AI 搜索账户？
            </a>
            <section>
             <p>
              Azure AI 搜索在全新
              <a href="https://portal.azure.cn/">
               Azure 门户
              </a>
              中提供。首先必须注册 Azure 订阅，可以通过预览门户中的库将
                                                Azure AI 搜索帐户添加到 Azure 订阅。
              <a href="https://docs.azure.cn/zh-cn/search/search-manage">
               获取详细信息
              </a>
              。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="warehouse-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <!--<p>SQL 数据仓库功能预览期间，不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问<a href="/support/legal/sla/" id="pricing_sql-database-warehouse_sla">服务级别协议</a>页。</p> -->
        <p>
         我们保证，至少在 99.9% 的时间内，在 SQL 数据仓库数据库上执行的客户端操作都会成功。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/search/v1_0/index.html" id="pricing_sql-database-warehouse_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token =
            '<input name="__RequestVerificationToken" type="hidden" value="FtJS7EhZBT6rgbp0PkQRqZsvT4EkKorxier871BisK9SjwrzF-3NCsrqEl_VYuhENbkKwc9cRYh6MY_Z2AzDIMXeWXd32LE1Vl7rUDgXkV41" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain +
            "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
