<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="免费云, azure container service, Azure" name="keywords" />
    <meta content="请参阅 Azure Kubernetes 服务 (AKS) 的定价详细信息。无前期成本。即用即付。免费试用。" name="description" />
    <title>
        定价 - Container Service - Azure 云计算
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="/Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/kubernetes-service" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css?2019/6/10 5:37:29" rel="stylesheet" />
    <link href="/Static/CSS/common.min.css?2019/6/10 5:37:29" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css?2019/6/10 5:37:29" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/10 5:37:29";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="kubernetes-service" wacn.date="11/27/2015">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'//wacnppe.blob.core.chinacloudapi.cn/marketing-resource/css/kubernetes-service_banner.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img
                                        src="//wacnppe.blob.core.chinacloudapi.cn/marketing-resource/css/<EMAIL>" />
                                    <h2>
                                        Azure Kubernetes 服务 (AKS)
                                    </h2>
                                    <h4>
                                        高度可用、安全且完全托管的 Kubernetes 服务
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <h4>
                                Azure Kubernetes 服务 (AKS)
                            </h4>
                            <p>
                                作为完全托管的 Kubernetes 容器业务流程协调程序服务，Azure Kubernetes 服务 (AKS) 是一种免费的容器服务，可以简化 Kubernetes
                                的部署、管理和操作。仅针对所使用的虚拟机、相关存储和网络资源计费，使 AKS 成为市场上最有效、最具成本效益的容器服务。
                            </p>
                        </div>

                        <table cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <th align="left">

                                </th>
                                <th align="left">
                                    <strong>
                                        免费层
                                    </strong>
                                </th>
                                <th align="left">
                                    <strong>
                                        标准层
                                    </strong><br>
                                    ￥0.636 /群集/小时
                                </th>
                                <th align="left">
                                    <strong>
                                        高级层
                                    </strong><br>
                                    ￥3.82 /群集/小时
                                </th>
                            </tr>
                            <tr>
                                <td>
                                    推荐用于
                                </td>
                                <td>
                                    学习、探索、试验
                                </td>
                                <td>
                                    所有任务关键型、大规模工作负荷或生产工作负荷
                                </td>
                                <td>
                                    需要 2 年支持的所有任务关键型、大规模或生产工作负载
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    节点定价
                                </td>
                                <td>
                                    仅为你使用的部分付费
                                </td>
                                <td>
                                    仅为你使用的部分付费
                                </td>
                                <td>
                                    仅为你使用的部分付费
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    群集管理
                                </td>
                                <td>
                                    免费
                                </td>
                                <td>
                                    1 个运行时间 SLA 小时
                                </td>
                                <td>
                                    1 个高级支持小时
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    服务级别协议 (SLA)
                                </td>
                                <td>
                                    N/A
                                </td>
                                <td>
                                    财务支持的 API 服务器运行时间 SLA
                                </td>
                                <td>
                                    财务支持的 API 服务器运行时间 SLA
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    长期支持
                                </td>
                                <td>
                                    社区支持
                                </td>
                                <td>
                                    社区支持
                                </td>
                                <td>
                                    社区支持结束后的 Microsoft 
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Kubernetes 控制平面
                                </td>
                                <td>
                                    无自动缩放
                                </td>
                                <td>
                                    API 服务器自动缩放
                                </td>
                                <td>
                                    API 服务器自动缩放 
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    
                                </td>
                                <td>
                                    较小的控制平面限制
                                </td>
                                <td>
                                    更大的控制平面限制
                                </td>
                                <td>
                                    更大的控制平面限制 
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    AKS 群集节点限制
                                </td>
                                <td>
                                    1,000 (建议不要在超过 10 个节点刻度)
                                </td>
                                <td>
                                    5,000
                                </td>
                                <td>
                                    5,000 
                                </td>
                            </tr>
                        </table>


                        <div class="pricing-page-section">
                            <h4>
                                群集管理免费
                            </h4>
                            <p>
                                群集管理不收取任何费用。
                            </p>
                        </div>
                        <div class="pricing-page-section">
                            <h4>
                                节点定价 - 仅为你使用的部分付费
                            </h4>
                            <p>
                                只需为 Kubernetes 群集所使用的虚拟机实例、存储空间和网络资源付费。
                            </p>
                        </div>
                        <h4>
                            添加运行时间服务级别协议的选项
                        </h4>
                        <p>
                            受财务支持的服务级别协议 (SLA) 对使用 Azure 可用性区域的群集的 Kubernetes API 服务器保证 99.95% 的运行时间；对不使用 Azure
                            可用性区域的群集的 Kubernetes API 服务器保证 99.9% 的运行时间。
                        </p>
                        <table cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <th align="left">
                                    <strong>
                                        计量
                                    </strong>
                                </th>
                                <th align="left">
                                    <strong>
                                        价格
                                    </strong>
                                </th>
                            </tr>
                            <tr>
                                <td>
                                    运行时间 SLA
                                </td>
                                <td>
                                    ￥0.636/群集/小时
                                </td>
                            </tr>
                        </table>
                        <div class="tags-date">
                            <div class="ms-date">
                                提示：AKS 运行时间价格将于2021年5月1日开始生效
                            </div>
                        </div>
                        <div class="pricing-page-section">
                            <h4>
                                IP 地址选项
                            </h4>
                            <p>
                                每项包含一台或多台 Azure 虚拟机的 Azure 云服务都被自动分配了一个免费的动态虚拟 IP (VIP) 地址。如果另外付费，你还可以获得：
                            </p>
                            <ul>
                                <li>
                                    实例级别公共 IP 地址 - 动态公共 IP (PIP) 地址，分配给虚拟机用于直接访问。
                                </li>
                                <li>
                                    保留 IP 地址 - 可以保留并用作 VIP 地址的公共 IP 地址。
                                </li>
                                <li>
                                    负载均衡的 IP 地址 - 额外的负载均衡 VIP 地址，可以分配给包含一台或多台 Azure 虚拟机的 Azure 云服务。
                                </li>
                            </ul>
                            <p>
                                <a href="https://www.azure.cn/pricing/details/ip-addresses">
                                    请参见 IP 地址定价
                                </a>
                            </p>
                        </div>
                        <div class="pricing-page-section">
                            <div class="more-detail">
                                <h2>
                                    常见问题
                                </h2>
                                <em>
                                    全部展开
                                </em>
                                <ul>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Data_Transfer_region">
                                                为什么我们启用了 AKS 之后，产生大量的 Azure 监控器费用？
                                            </a>
                                            <section>
                                                <p>
                                                    Azure 监视器提供了对容器及 AKS 节点非常强大和有用的日志记录和指标。也因此产生一定的数据提取成本。创建 AKS
                                                    时，请查看是否需要开启“监视器”如果不需要请手动禁用此功能，否则有可能会产生监视的费用，您可以参考
                                                    <a href="/pricing/details/monitor/index.html">
                                                        Azure 价格监控器价格详情
                                                    </a>
                                                    获得更多详细信息。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="pricing-page-section">
                            <h2>
                                支持和服务级别协议
                            </h2>
                            <p>
                                作为一项免费服务，AKS 不提供资金支持的服务级别协议。我们致力于让 Kubernetes API 服务器保持至少 99.5% 的可用性。虚拟机 SLA
                                涵盖群集中代理节点的可用性。有关详细信息，请参阅
                                <a href="https://www.azure.cn/support/sla/virtual-machines/">
                                    虚拟机
                                    SLA
                                </a>
                                。
                            </p>
                        </div>
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="YhZEsw0hBO4jck2dSnNmk11cOR0MgyWjpozeOhOilyQ9KHSr36LV2HGYYAIT5nbPgxTbxEMF6idVogQpTTwZRIa9dtjR10EMAFkS7spkW381" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/eb63d853f38b0d234f105aee5fc58ac137b3bb5d.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>