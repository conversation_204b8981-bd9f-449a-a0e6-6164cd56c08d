<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Microsoft Cloud, Azure Container Registry, Container Registry pricing details, pricing, billing" name="keywords"/>
    <meta content="Azure Container Registry is a service to manage private Docker registries for common storage across all your Azure container deployments. Begin a free trial today. A 1RMB Trial gets you ￥1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer."
          name="description"/>
    <title>
        Container Registry Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/container-registry/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="12/10/2018" ms.service="en-us-container-registry" wacn.date="12/10/2018">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/container-registry_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/media/images/production/container-registry.svg"/>
                                <h2>
                                    Container Registry
                                    <!--span>App Service</span-->
                                </h2>
                                <h4>
                                    Manage a Private Docker Registry as a first-class Azure resource
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <div class="pricing-page-section">
                                <p>
                                    Azure Container Registry provides storage of private Docker container images,
                                    enabling fast, scalable retrieval, and
                                    network-close deployment of container workloads on Azure. Additional
                                    capabilities include geo-replication, image
                                    signing with Docker Content Trust, Helm Chart Repositories and Task base compute
                                    for building, testing, patching
                                    container workloads.
                                </p>
                            </div>
                            <h3>
                                Pricing details
                            </h3>
                            <div class="tags-date" data-filtered="filtered">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per
                                    month.
                                </div>
                            </div>
                        </div>
                        <div class="scroll-table">
                            <table cellpadding="0" cellspacing="0">
                                <thead>
                                <tr>
                                    <th align="left" width="25%">
                                    </th>
                                    <th align="left" width="25%">
                                        <strong>
                                            BASIC
                                        </strong>
                                    </th>
                                    <th align="left" width="25%">
                                        <strong>
                                            STANDARD
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            PREMIUM
                                        </strong>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="left_align">
                                        Price per day
                                    </td>
                                    <td class="left_align">
                                        1.6953
                                    </td>
                                    <td class="left_align">
                                        6.784
                                    </td>
                                    <td class="left_align">
                                        16.96
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Included storage (GB)
                                    </td>
                                    <td class="left_align">
                                        10 GB
                                    </td>
                                    <td class="left_align">
                                        100 GB
                                    </td>
                                    <td class="left_align">
                                        500Premium offers enhanced throughput for docker
                                        pulls across multiple, concurrent nodes
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Total web hooks
                                    </td>
                                    <td class="left_align">
                                        2
                                    </td>
                                    <td class="left_align">
                                        10
                                    </td>
                                    <td class="left_align">
                                        500(additional available upon request)
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Geo Replication
                                    </td>
                                    <td class="left_align">
                                        Not Supported
                                    </td>
                                    <td class="left_align">
                                        Not Supported
                                    </td>
                                    <td class="left_align">
                                        Supported16.96/per replicated region
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <p>
                            Standard networking charges apply.
                        </p>
                        <h3>
                            Additional storage
                        </h3>
                        <p>
                            Additional storage is available at a daily rate for all service tiers. You're not
                            prevented from storing more than the included storage limits for each tier, but you are
                            charged a daily rate for image storage beyond the specified limits.
                        </p>
                        <table cellpadding="0" cellspacing="0">
                            <thead>
                            <tr>
                                <th align="left">
                                    TIER
                                </th>
                                <th align="left">
                                    PRICE PER GB
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    Basic, Standard, Premium
                                </td>
                                <td>
                                    ￥1.0176/month
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <h3>
                            Container Build
                        </h3>
                        <table cellpadding="0" cellspacing="0">
                            <thead>
                            <tr>
                                <th align="left">
                                    TIER
                                </th>
                                <th align="left">
                                    PRICE PER CPU
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    Basic, Standard, Premium
                                </td>
                                <td>
                                    ￥0.000508/second
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <!-- END: Table2-Content-->
                    </div>
                    <!-- END: TAB-CONTAINER-1 -->
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em id="ws_unfolded_all">
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="container-registry-que-q1">
                                            Can I exceed the usage limit on each tier?
                                            If yes, how does billing work for
                                            overage usage? If no, what happens to service when I reach the
                                            limit?
                                        </a>
                                        <section>
                                            <p>
                                                Basic, Standard and Premium tiers include an amount of storage as
                                                part of their pricing. As storage
                                                exceeds the included amount, registries are charged for their
                                                additional storage.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="container-registry-que-q2">
                                            Can I upgrade or downgrade the service
                                            tier? If yes, how does that impact my
                                            billing?
                                        </a>
                                        <section>
                                            <p>
                                                Yes, it’s possible to change the SKU in the managed tier using the
                                                Azure CLI. Users will be charged at
                                                the price for the previous SKU up until the point of the change, and
                                                with the new SKU price after the
                                                change is made.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="container-registry-que-q3">
                                            How does the usage show up on my bill?
                                        </a>
                                        <section>
                                            <p>
                                                The bill will display the cost of each managed registry based on the
                                                SKU of the registry, as well as any
                                                network charges.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="container-registry-que-q4">
                                            Do I incur data transfer charges?
                                        </a>
                                        <section>
                                            <p>
                                                Standard networking fees apply to network egress from or between
                                                azure data centers. We recommend
                                                maintaining a registry in the same datacenter as your container
                                                deployments. See the geo-replication
                                                preview feature to manage multiple regions as one, providing
                                                network-close deployments across multiple
                                                regions.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <div class="pricing-page-section">
                                     <h2>Region</h2>
                                     <p>App Service is available in the following regions:</p>
                                     <table cellpadding="0" cellspacing="0" class="table-col6">
                                         <tr>
                                             <th align="left"><strong>Territory</strong></th>
                                             <th align="left"><strong>Region</strong></th>
                                         </tr>
                                         <tr>
                                             <td>Mainland China</td>
                                             <td>China North</td>
                                         </tr>
                                     </table>
                                 </div> -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="app-contact-page">
                                Azure
                                Support
                            </a>
                            and select self-help service or any other method to contact us for
                            support.
                        </p>
                        <p>
                            To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/container-registry/index.html" id="pricing_container-registry_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                             <h2>Support &amp; SLA</h2>
                             <p>Azure 支持功能：</p>
                             <p>我们免费向用户提供以下支持服务：</p>
                             <table cellpadding="0" cellspacing="0" class="table-col6">
                                 <tr>
                                     <th align="left">&nbsp;</th>
                                     <th align="left"><strong>是否支持</strong></th>
                                 </tr>
                                 <tr>
                                     <td>计费和订阅管理</td>
                                     <td><i class="icon icon-tick"></i></td>
                                 </tr>
                                 <tr>
                                     <td>服务仪表板</td>
                                     <td><i class="icon icon-tick"></i></td>
                                 </tr>
                                 <tr>
                                     <td>Web事件提交</td>
                                     <td><i class="icon icon-tick"></i></td>
                                 </tr>
                                 <tr>
                                     <td>中断/修复不受限制</td>
                                     <td><i class="icon icon-tick"></i></td>
                                 </tr>
                                 <tr>
                                     <td>电话支持</td>
                                     <td><i class="icon icon-tick"></i></td>
                                 </tr>
                                 <tr>
                                     <td>ICP备案支持</td>
                                     <td><i class="icon icon-tick"></i></td>
                                 </tr>
                             </table>
                             <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                             <h2>服务热线：</h2>
                             <ul>
                                 <li>400-089-0365</li>
                                 <li>010-84563652</li>
                             </ul>
                             <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                             <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                     -->
                    <!--END: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="FtJS7EhZBT6rgbp0PkQRqZsvT4EkKorxier871BisK9SjwrzF-3NCsrqEl_VYuhENbkKwc9cRYh6MY_Z2AzDIMXeWXd32LE1Vl7rUDgXkV41" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
