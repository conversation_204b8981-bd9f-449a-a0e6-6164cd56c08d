<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure 云服务, Cloud Service, 价格页面" name="keywords"/>
  <meta content="了解 Azure云服务（Cloud Service）部署应用程序和 API 的价格详情。Azure 云服务分为 A 系列和 D 系列两种类型，无需预付，即用即付。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   Azure Migrate
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/zh-cn/services/azure-migrate/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="cloud-services1" wacn.date="11/27/2015">
       </tags>
       <style>
        .pure-content table tr td{
                    line-height: 20px;
                }
                .pricing-detail-tab .tab-content .tab-panel.show-md {
                    display: block;
                }
                .pricing-detail-tab .tab-content .tab-panel {
                    display: none;
                }
                .ex_ul_container {
                    background-color: #f4f5f6;
                    overflow: hidden;
                }
                .ex_ul_container > li {
                    list-style-type: none;
                    width: 50%;
                    float: left;
                    padding: 2%;
                }
                #in_ul_container > li {
                    list-style-type: none;
                }
                .svg{
              					 width: 50px;
               					 float: left;
              					 margin-right: 10px;
           						}
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/cloud_services.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <!-- <img src="/Images/marketing-resource/css/<EMAIL>" /> -->
          <img src="/Images/marketing-resource/media/images/production/AzureMigratelogo.svg"/>
          <h2>
           Azure Migrate
          </h2>
          <h4>
           发现和评估本地虚拟机 (VM)，将其调整为适当大小并迁移到 Azure
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <ul class="ex_ul_container">
        <li>
         <h2>
          发现和评估本地虚拟机 (VM)，将其调整为适当大小并迁移到 Azure
         </h2>
        </li>
        <li>
         <ul id="in_ul_container">
          <li>
           Azure Migrate 有助于发现和评估应用程序、基础结构和数据，并有助于将其从本地环境迁移到 Azure。可以跨多种 Microsoft 和独立软件供应商 (ISV) 工具，集中跟踪迁移过程的进度。
          </li>
         </ul>
        </li>
       </ul>
       <h2>
        定价详细信息
       </h2>
       <h3>
        应用筛选器来根据你的需求自定义定价选项。
       </h3>
       <div class="tab-content">
        <table cellpadding="0" cellspacing="0" class="speech-services-table" id="cognitive-services-table-speech-services-neural" width="100%">
         <tr>
          <th align="left" style="width: 40%;">
           <strong>
            工具
           </strong>
          </th>
          <th align="left" style="width: 20%;">
           <strong>
            方案
           </strong>
          </th>
          <th align="left" style="width: 20%;">
           <strong>
            定价
           </strong>
          </th>
         </tr>
         <tr>
          <td rowspan="3">
           Azure Migrate
          </td>
          <td>
           服务器评估和迁移
          </td>
          <td>
           免费
           <sup>
            1
           </sup>
          </td>
         </tr>
         <tr>
          <td>
           数据库评估和迁移
          </td>
          <td>
           免费
           <sup>
            2
           </sup>
          </td>
         </tr>
         <td>
          Web 应用评估和迁移
         </td>
         <td>
          免费
          <sup>
          </sup>
         </td>
        </table>
        <p>
         <sup>
          1
         </sup>
         从将 Log Analytics 工作区与服务器评估进行关联之日算起，在前 180 天内，依赖项可视化效果可供免费使用。180 天之后，将收取标准
         <a href="https://www.azure.cn/zh-cn/pricing/details/monitor/">
          Log Analytics
         </a>
         费用。在此工作区中使用除服务映射外的任何其他解决方案不提供免费服务，并且会产生标准
         <a href="https://www.azure.cn/zh-cn/pricing/details/monitor/">
          Log Analytics
         </a>
         费用。Azure Migrate: 对于每台计算机，服务器迁移在前 180 天内免费。180 天之后，每个复制的实例将按 $31.301/月的标准收取费用。请注意，前 180 天内不会产生任何服务器迁移许可费用，但复制期间可能会产生 Azure 存储、存储交易和数据传输方面的费用。
        </p>
        <p>
         <sup>
          2
         </sup>
         数据库迁移服务 (DMS) 在前 180 天内可供免费使用，如需了解详细信息，请参阅
         <a href="https://www.azure.cn/zh-cn/pricing/details/database-migration/">
          DMS
         </a>
         定价。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <!-- END: TAB-CONTROL -->
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>云服务 A 系列 和 D 系列实例在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table>
            </div> -->
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="cSGgDkECxciH4aUZY54guVapLzF81y6JmPUFQyss2c7uLPMM65TofEN5fE_ME9Dfi2bJDdM87rIQN1v3-uHR7wzSh5Mj4BFmJZdJWkPn7tU1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
