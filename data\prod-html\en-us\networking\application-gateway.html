<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure Microsoft Cloud, Azure Application Gateway, Load Balancing Website Services HTTP Load Balancing" name="keywords"/>
    <meta content="Pricing details for Azure Application Gateway. Using Azure Application Gateway enables you to build highly scalable and available websites by providing HTTP load balancing and delivery control. "
          name="description"/>
    <title>
        Application Gateway Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/application-gateway/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-application-gateway" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a,
                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/app_gateway.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Application Gateway
                                    <!--span>Application Gateway</span-->
                                </h2>
                                <h4>
                                    Build secure, scalable, and highly available web front ends in Azure
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            By providing HTTP load balancing and delivery control, Azure Application Gateway permits
                            you to build highly scalable
                            and highly available websites.
                        </p>
                        <h2>
                            Pricing Details
                        </h2>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Application Gateway
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_application-gateway">
                                                        Application Gateway
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="Application Gateway">
                                                Application Gateway
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China East 2
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li>
                                                    <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                                                        China East 3
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>
                                                <li class="active">
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                        China East
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                        China North
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#east-china3" value="east-china3">
                                                China East 3
                                            </option>
                                            <option data-href="#north-china3" value="north-china3">
                                                China North 3
                                            </option>
                                            <option data-href="#east-china2" selected="selected" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content">
                            <!-- BEGIN: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 navigator 2 -->
                                <!-- BEGIN: Tab level 2 content 3 -->
                                <div class="tab-content">
                                    <!-- BEGIN: Table1-Content-->
                                    <h3>
                                        Application Gateway
                                    </h3>
                                    <p>
                                        We charge for Application Gateway based on the settings and the quantity of
                                        time providing the gateway, as well as
                                        the amount of data processed by Application Gateway.
                                    </p>
                                    <!-- <p>从2016年4月1日起，价格会下调 25.5%，以下是下调后的新价格：</p> -->
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            *The following prices are tax-inclusive.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            *Monthly price estimation is based on a usage of 744
                                            hours every month.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            * When using multiple instances, you are charged per
                                            instance.
                                        </div>
                                    </div>
                                    <table cellpadding="0" cellspacing="0" id="application-gateway-1" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                    Application Gateway Types
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Basic Application Gateway
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Web Application Firewall (WAF) Application
                                                    Gateway
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Small
                                            </td>
                                            <td>
                                                ￥0.16 per gateway/instance/hour (about ￥119.04/month)
                                            </td>
                                            <td>
                                                Not available
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Medium
                                            </td>
                                            <td>
                                                ￥0.45 per gateway/instance/hour (about ￥334.80/month)
                                            </td>
                                            <td>
                                                ￥1.80 per gateway/instance/hour (about ￥1,339.20/month)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Large
                                            </td>
                                            <td>
                                                ￥2.01 per gateway/instance/hour (about ￥1,495.44/month)
                                            </td>
                                            <td>
                                                ￥6.38 per gateway/instance/hour (about ￥4,746.72/month)
                                            </td>
                                        </tr>
                                    </table>
                                    <table cellpadding="0" cellspacing="0" id="application-gateway-2" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                    Application Gateway Types
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Basic Application Gateway
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Web Application Firewall (WAF) Application
                                                    Gateway
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Small
                                            </td>
                                            <td>
                                                ￥0.16 per gateway/instance/hour (about ￥119.04/month)
                                            </td>
                                            <td>
                                                Not available
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Medium
                                            </td>
                                            <td>
                                                ￥0.45 per gateway/instance/hour (about ￥334.80/month)
                                            </td>
                                            <td>
                                                ￥1.80 per gateway/instance/hour (about ￥1,339.20/month)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Large
                                            </td>
                                            <td>
                                                ￥2.01 per gateway/instance/hour (about ￥1,495.44/month)
                                            </td>
                                            <td>
                                                ￥6.38 per gateway/instance/hour (about ￥4,746.72/month)
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- END: Table1-Content-->
                                    <!-- BEGIN: Table2-Content-->
                                    <h3>
                                        Data processing
                                    </h3>
                                    <p>
                                        The charges for data processing are based on the amount of data processed by
                                        the Application Gateway.
                                    </p>
                                    <!-- <p>从2016年4月1日起，数据处理的价格会下调 25.5%，以下是下调后的新价格：</p> -->
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            *The following prices are tax-inclusive.
                                        </div>
                                        <br/>
                                        <div class="ms-date">
                                            *Monthly price estimates are based on 744 hours of
                                            usage per month.
                                        </div>
                                    </div>
                                    <table cellpadding="0" cellspacing="0" width="100%">
                                        <tr>
                                            <th align="left">
                                                Data processing
                                            </th>
                                            <th align="left">
                                                Small
                                            </th>
                                            <th align="left">
                                                Medium
                                            </th>
                                            <th align="left">
                                                Large
                                            </th>
                                        </tr>
                                        <tr>
                                            <td style="min-width:100px;">
                                                First 10 TB/month
                                            </td>
                                            <td style="min-width:100px;">
                                                ￥0.05/GB
                                            </td>
                                            <td style="min-width:50px;">
                                                Free
                                            </td>
                                            <td style="min-width:50px;">
                                                Free
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="min-width:100px;">
                                                Last 30TB (10 - 40 TB)/month
                                            </td>
                                            <td style="min-width:100px;">
                                                ￥0.05/GB
                                            </td>
                                            <td style="min-width:50px;">
                                                ￥0.04/GB
                                            </td>
                                            <td style="min-width:50px;">
                                                Free
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="min-width:100px;">
                                                Over 40 TB/month
                                            </td>
                                            <td style="min-width:100px;">
                                                ￥0.05/GB
                                            </td>
                                            <td style="min-width:50px;">
                                                ￥0.04/GB
                                            </td>
                                            <td style="min-width:50px;">
                                                ￥0.02/GB
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- END: Table2-Content-->
                                    <!-- BEGIN: Table3-Content-->
                                    <h3>
                                        Inbound data transfers
                                    </h3>
                                    <p>
                                        (i.e. data going into Azure data centers) -
                                        <b>
                                            Free
                                        </b>
                                    </p>
                                    <!-- END: Table3-Content-->
                                    <!-- BEGIN: Table4-Content-->
                                    <h3>
                                        Outbound data transfers
                                    </h3>
                                    <p>
                                        (Namely, data transmission from Azure Datacenter): data transmitted from
                                        Azure Datacenter via Application Gateway
                                        will be charged at the standard
                                        <a class="rolecashLink" href="/en-us/pricing/details/data-transfer/" id="redis_roleCaching_cloudService" target="_blank">
                                            Data
                                            Transmission Rate
                                        </a>
                                        .
                                    </p>
                                    <!-- END: Table4-Content-->
                                    <div class="scroll-table" style="display: block;">
                                        <h3>
                                            Azure Application Gateway V2 
                                        </h3>
                                        <p>
                                            Azure Application Gateway V2 offer support for autoscaling, zone redundancy, and Static VIP. These gateways also offer enhanced performance, better provisioning, and configuration update time, header rewrites, and WAF custom rules. Please refer to
                                            <a href="https://docs.microsoft.com/en-us/azure/application-gateway/application-gateway-autoscaling-zone-redundant">
                                                documentation
                                            </a>
                                            for additional product details, as well as the FAQ section below for pricing and billing information.
                                        </p>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                *The following prices are tax-inclusive.
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *Monthly price estimation is based on a usage of
                                                744 hours every month.
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *The pricing as below will be effective starting
                                                from September 1
                                                <sup>
                                                    st
                                                </sup>
                                                , 2020.
                                            </div>
                                        </div>
                                        <table cellpadding="0" cellspacing="0" id="application-gateway-table-standard-v2" width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Application Gateway Basic Edition
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Application Gateway Standard
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Web Application Firewall
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Fixed
                                                </td>
                                                <td>
                                                    ¥0.156 per gateway-hour
                                                </td>
                                                    
                                                <td>
                                                    ¥2.035 per gateway-hour
                                                </td>
                                                <td>
                                                    ¥3.663 per gateway-hour
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Capacity Unit
                                                    <sup>
                                                        1
                                                    </sup>
                                                </td>
                                                <td>
                                                    ¥0.072 per capacity unit-hour
                                                </td>
                                                <td>
                                                    ¥0.081 per capacity unit-hour
                                                </td>
                                                <td>
                                                    ¥0.147 per capacity unit-hour
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                <sup>
                                                    1
                                                </sup>
                                                For more information on Capacity Unit,
                                                please refer to the FAQ section at the bottom of the page.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- END: TAB-CONTAINER-3 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="pricing_redis_question1">
                                            Will usage of less than one hour be billed
                                            as one hour?
                                        </a>
                                        <section>
                                            <p>
                                                Yes, usage of less than one hour will be billed as one hour.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="pricing_redis_question3">
                                            Is a Service Level Agreement offered for an
                                            application cloud service with only one
                                            instance? What about small application gateways?
                                        </a>
                                        <section>
                                            <p>
                                                No. The small-sized Application Gateway is mainly used for
                                                development/testing purposes. Running production
                                                workloads on a small-sized Application Gateway may exceed the
                                                capacity of small instances. Application Gateway
                                                supports deploying one or several instances in the same cloud
                                                service. Due to a lack of high availability,
                                                running the Application Gateway service with only one instance is
                                                not recommended.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="pricing_faq_application-gateway">
                                            What is Capacity Units?
                                        </a>
                                        <section>
                                            <p>
                                                Capacity Units measure consumption-based cost that is charged in
                                                addition to the fixed cost. Capacity unit charge
                                                is also computed hourly or partial hourly. There are three
                                                dimensions to capacity unit - compute unit,
                                                persistent connections, and throughput. Compute unit is a measure of
                                                processor capacity consumed. Please refer
                                                to our
                                                <a href="https://docs.microsoft.com/en-us/azure/application-gateway/application-gateway-autoscaling-zone-redundant#pricing">
                                                    documentation
                                                    page
                                                </a>
                                                .
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="gateway-contact-page">
                                Azure Support
                            </a>
                            to select self-service or any other
                            method to contact us for support.
                        </p>
                        <p>
                            We guarantee every Application Gateway service with two or more medium- and large-sized
                            instances is available at least
                            99.9% of the time. We don’t provide service level agreements for Application Gateway
                            service with only one instance or
                            small instances. If you want to learn about the details of our server level agreement,
                            please access the
                            <a href="/en-us/support/sla/application-gateway" id="pricing_gateway_sla">
                                Service Level Agreement
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure Support Features:</p>
                         <p>We provide users with the following free support services:</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left"> </th>
                                 <th align="left"><strong>Supported or not</strong></th>
                             </tr>
                             <tr>
                                 <td>Billing and Subscription Management</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Service Dashboard</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web Event Submission</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Unlimited Disruption/Restoration</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Telephone Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP Filing Support</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>You can <a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">request support online</a> or contact us via the service hotline.</p>
                         <h2>Service hotline:</h2>
                         <ul>
                             <li>************</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>Community help: <a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">Visit MSDN</a></p>
                         <p>For more support information, please visit <a href="/en-us/support/plans/" id="stor-sla-info">Azure Support Plans</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static//Scripts/lib/jquery-1.12.3.min.js">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="lhor9v1qqJ4mP3lG5HURLAZABept4p__AsPMo2j1e_nA6vZfwPLI7gqslisxPJDwqCUH7z1ZnCP9e71ljPbgahJBkpB5GvE9o4jG1UyT-Po1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
