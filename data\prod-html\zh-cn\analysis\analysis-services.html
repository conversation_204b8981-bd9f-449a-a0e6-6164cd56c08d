<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure 微软云, Azure 分析服务, 价格详情定价计费" name="keywords"/>
  <meta content="了解Azure 分析服务的价格详情。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   Azure 分析服务价格_Azure 分析服务价格估算 -  Azure云服务
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/analysis-services/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="analysis-services" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_analysis-services.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/images/service/<EMAIL>"/>
          <h2>
           Azure 分析服务
           <span>
            Azure Analysis Services
           </span>
          </h2>
          <h4>
           企业级分析引擎即服务
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <h2>
         经过验证的分析引擎
        </h2>
        <p>
         Azure 分析服务提供具有可伸缩性、灵活性和云管理优势的企业级 BI 语义建模功能。Azure 分析服务有助于将复杂数据转换为可操作见解。Azure 分析服务基于 Microsoft SQL Server Analysis Services 中经认证的分析引擎构建。
        </p>
        <h2>
         定价详细信息
        </h2>
        <p>
         Azure 分析服务的总成本取决于所选的层和实例。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Analysis Services
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_analysis-services">
                Analysis Services
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Analysis Services">
              Analysis Services
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-3 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <div class="tab-content">
           <!-- BEGIN: Table1-Content-->
           <!-- <p>可在开发人员层、基本层和标准层使用 Azure 分析服务。层之间的主要特征差异如下表所示：</p> -->
           <p>
            可在基本层和标准层使用 Azure 分析服务。层之间的主要特征差异如下表所示：
           </p>
           <table cellpadding="0" cellspacing="0" width="100%">
            <thead>
             <tr>
              <th align="left">
               <strong>
                功能
               </strong>
              </th>
              <!-- <th align="left"><strong>开发人员</strong></th> -->
              <th align="left">
               <strong>
                基本
               </strong>
              </th>
              <th align="left">
               <strong>
                标准
               </strong>
              </th>
             </tr>
            </thead>
            <tbody>
             <tr>
              <td class="left_align">
               透视
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               多个分区
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               DirectQuery Storage 模式
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               翻译
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
               √
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               DAX 计算
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
               √
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               行级安全
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
               √
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               In-mem 存储
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
               √
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
             <tr>
              <td class="left_align">
               备份和还原
              </td>
              <!-- <td class="left_align">√</td> -->
              <td class="left_align">
               √
              </td>
              <td class="left_align">
               √
              </td>
             </tr>
            </tbody>
           </table>
           <p>
            在每个层中，实例的价格会因处理能力、QPU 和内存大小而异。
           </p>
           <!-- <h3>开发人员层</h3>							                   
                            <p>对于评估、开发和测试方案，建议使用开发人员层。它包含标准层的全部功能，但处理能力、QPU 和内存大小有限制。开发人员层不提供服务级别协议。</p>
                            <div class="tags-date">                       
                                <div class="ms-date">*以下价格均为含税价格。</div><br />
                                <div class="ms-date">*每月价格估算基于每个月 744 小时的使用量。</div>
                            </div>
                            <table  cellpadding="0" cellspacing="0" width="100%">
                                <thead>
                                    <tr>
                                    <th align="left"><strong>实例</strong></th>
                                    <th align="left"><strong>QPU</strong></th>
                                    <th align="left"><strong>内存 (GB)</strong></th>
                                    <th align="left"><strong>服务级别协议</strong></th>
                                    <th align="left"><strong>价格<sup style="font-weight: normal;">1</sup></strong></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="left_align">开发人员</td>
                                        <td class="left_align">20</td>
                                        <td class="left_align">3</td>
                                        <td class="left_align">无</td>
                                        <td class="left_align">￥ 0.8395/小时<br/>（~￥ 624.588/月）</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="tags-date">
                                <div class="ms-date"><sup style="font-weight: normal;">1 </sup></strong>收取标准数据传输费率。</div>                       
                            </div> -->
           <h3>
            基本级别
           </h3>
           <p>
            基本层是常规用途层，建议在具有小型表格模型的生产解决方案、限制用户并发和要求简单数据刷新的情况中使用该层。
           </p>
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              B1
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥2.4475/小时
              <br/>
              (~￥1,820.94 /月)
             </td>
            </tr>
            <tr>
             <td>
              B2
             </td>
             <td>
              80
             </td>
             <td>
              20
             </td>
             <td>
              ￥4.895/小时
              <br/>
              (~￥3,641.88 /月)
             </td>
            </tr>
           </table>
           <div class="tags-date">
            <div class="ms-date">
             <sup style="font-weight: normal;">
              1
             </sup>
             收取标准数据传输费率。
            </div>
           </div>
           <h3>
            标准级别
           </h3>
           <p>
            对于需求弹性用户并发，且拥有不断发展的数据模型的任务关键生产应用程序而言，最适合选择标准层。标准层的高级数据刷新功能有助于客户实现几乎实时的数据模型更新。
           </p>
           <table cellpadding="0" cellspacing="0" id="analysis-services-standard-tier-s0" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S0
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥12.943/小时
              <br/>
              (~￥ 9,629.592/月)
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="analysis-services-standard-tier-new-s2" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S0
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥12.943/小时
              <br/>
              (~￥ 9,629.592/月)
             </td>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              100
             </td>
             <td>
              25
             </td>
             <td>
              ￥21.571/小时
              <br/>
              (~￥ 16,048.824/月)
             </td>
            </tr>
            <tr>
             <td>
              S2
             </td>
             <td>
              200
             </td>
             <td>
              50
             </td>
             <td>
              ￥43.153/小时
              <br/>
              (~￥ 32,105.832/月)
             </td>
            </tr>
            <tr>
             <td>
              S4
             </td>
             <td>
              400
             </td>
             <td>
              100
             </td>
             <td>
              ￥86.316/小时
              <br/>
              (~￥ 64,219.104/月)
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="analysis-services-standard-tier-region2" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S0
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥12.943/小时
              <br/>
              (~￥ 9,629.592/月)
             </td>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              100
             </td>
             <td>
              25
             </td>
             <td>
              ￥21.571/小时
              <br/>
              (~￥ 16,048.824/月)
             </td>
            </tr>
            <tr>
             <td>
              S2
             </td>
             <td>
              200
             </td>
             <td>
              50
             </td>
             <td>
              ￥43.153/小时
              <br/>
              (~￥ 32,105.832/月)
             </td>
            </tr>
            <tr>
             <td>
              S4
             </td>
             <td>
              400
             </td>
             <td>
              100
             </td>
             <td>
              ￥86.316/小时
              <br/>
              (~￥ 64,219.104/月)
             </td>
            </tr>
            <tr>
             <td>
              S8 v2
             </td>
             <td>
              640
             </td>
             <td>
              200
             </td>
             <td>
              ￥ 106.700/小时
              <br/>
              (~￥ 79,384.8/月)
             </td>
            </tr>
            <tr>
             <td>
              S9 v2
             </td>
             <td>
              1280
             </td>
             <td>
              400
             </td>
             <td>
              ￥ 208.226/小时
              <br/>
              (~￥ 154,920.144/月)
             </td>
            </tr>
           </table>
           <h3>
            水平横向扩展
           </h3>
           <p>
            可将横向扩展实例添加到主实例，实现更快的数据和查询处理。
           </p>
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="analysis-services-standard-tier-s1" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S0
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥9.71/小时
              <br/>
              (~￥ 7,224.24/月)
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="analysis-services-standard-tier-new-s1" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S0
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥9.71/小时
              <br/>
              (~￥ 7,224.24/月)
             </td>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              100
             </td>
             <td>
              25
             </td>
             <td>
              ￥16.176/小时
              <br/>
              (~￥ 12,034.944/月)
             </td>
            </tr>
            <tr>
             <td>
              S2
             </td>
             <td>
              200
             </td>
             <td>
              50
             </td>
             <td>
              ￥32.362/小时
              <br/>
              (~￥ 24,077.328/月)
             </td>
            </tr>
            <tr>
             <td>
              S4
             </td>
             <td>
              400
             </td>
             <td>
              100
             </td>
             <td>
              ￥64.734/小时
              <br/>
              (~￥ 48,162.096/月)
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="analysis-services-standard-tier-region3" width="100%">
            <tr>
             <th align="left">
              <strong>
               实例
              </strong>
             </th>
             <th align="left">
              <strong>
               QPU
              </strong>
             </th>
             <th align="left">
              <strong>
               内存 (GB)
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
               <sup style="font-weight: normal;">
                1
               </sup>
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S0
             </td>
             <td>
              40
             </td>
             <td>
              10
             </td>
             <td>
              ￥9.71/小时
              <br/>
              (~￥ 7,224.24/月)
             </td>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              100
             </td>
             <td>
              25
             </td>
             <td>
              ￥16.176/小时
              <br/>
              (~￥ 12,034.944/月)
             </td>
            </tr>
            <tr>
             <td>
              S2
             </td>
             <td>
              200
             </td>
             <td>
              50
             </td>
             <td>
              ￥32.362/小时
              <br/>
              (~￥ 24,077.328/月)
             </td>
            </tr>
            <tr>
             <td>
              S4
             </td>
             <td>
              400
             </td>
             <td>
              100
             </td>
             <td>
              ￥64.734/小时
              <br/>
              (~￥ 48,162.096/月)
             </td>
            </tr>
            <tr>
             <td>
              S8 v2
             </td>
             <td>
              640
             </td>
             <td>
              200
             </td>
             <td>
              ￥ 106.700/小时
              <br/>
              (~￥ 79,384.8/月)
             </td>
            </tr>
            <tr>
             <td>
              S9 v2
             </td>
             <td>
              1280
             </td>
             <td>
              400
             </td>
             <td>
              ￥ 208.226/小时
              <br/>
              (~￥ 154,920.144/月)
             </td>
            </tr>
           </table>
           <!-- <table cellpadding="0" cellspacing="0" width="100%" id="analysis-services-standard-tier-region">
                                <tr>
                                    <th align="left"><strong>实例</strong></th>
                                    <th align="left"><strong>QPU</strong></th>
                                    <th align="left"><strong>内存 (GB)</strong></th>
                                    <th align="left"><strong>价格<sup style="font-weight: normal;">1</sup></strong></th>
                                </tr>
                                <tr>
                                    <td>S0</td>
                                    <td>40</td>
                                    <td>10</td>
                                    <td>￥12.948/小时<br>(~￥9,633.312 /月)</td>                            
                                </tr>
                                <tr>
                                    <td>S1</td>
                                    <td>100</td>
                                    <td>25</td>
                                    <td>￥21.58/小时<br>(~￥16,055.52 /月)</td>                            
                                </tr>
                                <tr>
                                    <td>S2</td>
                                    <td>200</td>
                                    <td>50</td>
                                    <td>￥43.16/小时<br>(~￥32,111.04 /月)</td>                            
                                </tr>
                                <tr>
                                    <td>S4</td>
                                    <td>400</td>
                                    <td>100</td>
                                    <td>￥86.32/小时<br>(~￥64,222.08 /月)</td>                            
                                </tr>
                            </table> -->
           <div class="tags-date">
            <div class="ms-date">
             <sup style="font-weight: normal;">
              1
             </sup>
             收取标准数据传输费率。
            </div>
           </div>
           <!-- <h3>标准级别 - 横向扩展</h3>
                            <p>扩展实例可以添加到主实例，实现更快的数据和查询处理。 扩展实例的定价如下：</p>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left" width="30%"><strong>实例</strong></th>
                                    <th align="left"><strong>价格</strong></th>
                                </tr>
                                <tr>
                                    <td>S0</td>
                                    <td>￥9.71/小时(~￥7,224.24 /月)</td>                            
                                </tr>
                                <tr>
                                    <td>S1</td>
                                    <td>￥16.19/小时(~￥12,041.36 /月)</td>                            
                                </tr>
                                <tr>
                                    <td>S2</td>
                                    <td>￥32.37/小时(~￥24,083.28 /月)</td>                            
                                </tr>
                                <tr>
                                    <td>S4</td>
                                    <td>￥64.74/小时(~￥48,166.56 /月)</td>                            
                                </tr>
                            </table> -->
           <!-- END: Table2-Content-->
          </div>
         </div>
         <!-- END: TAB-CONTAINER-3 -->
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em id="ws_unfolded_all">
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="analysis-services-que-q1">
             什么是 QPU？
            </a>
            <section>
             <p>
              Azure 分析服务中的查询处理单元 (QPU) 是一种度量单位，用于计算查询和数据处理的相对运算性能。标准层 S4 实例（可提供 400 个 QPU）的运算性能是标准层 S1 实例（可提供 100 个 QPU）的 4 倍。就经验法则而言，一个虚拟核心近似于约 20 个 QPU（尽管确切的性能取决于底层硬件和所用硬件的版本）。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="analysis-services-que-q2">
             可以按何种频率更改单个实例的服务层或性能级别？
            </a>
            <section>
             <p>
              客户可在层内更改性能级别。例如，客户可以从 S2 无缝移动到 S4，反之亦然。客户还可以从较低层移动到较高层，但无法从较高层移动到较低层。例如，可以从基本层移动到标准层，但无法从标准层移动到基本层。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="analysis-services-que-q3">
             Azure 分析服务的使用情况会以何种形式显示在账单上？
            </a>
            <section>
             <p>
              Azure 分析服务基于单个实例的服务层和性能级别两个方面针对可预测的小时费率进行收费。实际用量计算到秒，并按小时收费。例如，如果某个实例一个月内运行了 12 小时 15 分钟，则账单会显示使用了 12.25 小时。如果实例仅有 6 分钟处于活动状态，则账单会显示使用了 0.1 小时。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="analysis-services-que-q4">
             分析服务在暂停的状态下是否计费？
            </a>
            <section>
             <p>
              在暂停的状态下是不计费的。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>Azure 分析服务在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国北部数据中心</td>
                    </tr>
                </table> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="app-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证，在 Azure 分析服务的服务器上执行的客户端操作的成功率不低于 99.9%。若要了解有关我们的基本层和标准层中保证的服务级别协议的详细信息，请访问
         <a href="../../../support/sla/analysis-services/index.html" id="pricing_app_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="rO62GKiRoQZVwCXZcP5uzFHDzhB5TOwgTP-cUG5fOOY894KJpbP_TOQXfijNrylLG_IhjL5ieAQQiFVTchQwr-B9xCfajtLnWYViaK3JJBg1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
