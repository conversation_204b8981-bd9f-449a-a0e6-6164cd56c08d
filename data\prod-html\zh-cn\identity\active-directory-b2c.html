

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <meta name="keywords" content="iaas, 云基础结构即服务, active directory, 价格" />
    <meta name="description" content="Azure Active Directory B2C 是一个标识和访问管理云解决方案，适用于面向消费者的 Web 和移动应用。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" />

    <title>Active Directory External Identities - Azure云计算</title>

    <link rel="apple-touch-icon" sizes="180x180" href="../../../Static/Favicon/apple-touch-icon.png">
    <link rel="shortcut icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="manifest" href="../../../Static/Favicon/manifest.json">
    <link rel="mask-icon" href="../../../Static/Favicon/safari-pinned-tab.svg" color="#0078D4">
    <meta name="msapplication-config" content="/Static/Favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
        <link rel="canonical" href="https://azure.microsoft.com/pricing/details/active-directory-b2c/"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    
    <!-- END: Page Style -->

    <!-- BEGIN: Minified Page Style -->
    <link href='../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css' rel='stylesheet' />
    <!-- END: Minified Page Style -->
            
    <link rel="stylesheet" href="../../../StaticService/css/service.min.css" />
</head>
<body class="zh-cn">    
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    
    <style>
       @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
    </style>
<div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage"></div>
</div>
   
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li><span></span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select><option selected="selected">加载中...</option></select>
                        <span class="icon icon-arrow-top"></span>
                    </div>
                    
            <tags ms.service="active-directory-b2c" ms.date="09/30/2015" wacn.date="11/27/2015"></tags>
            <style>
                #aad-b2c-table-mau tr,#aad-b2c-table-separate tr{
                    background-color:rgb(255,255,255) !important;
                }
            </style>
            <!-- BEGIN: Product-Detail-TopBanner -->
            <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/activity_directory_b2c.png','imageHeight':'auto'}">
                <div class="common-banner-image">
                    <div class="common-banner-title">
                        <img src="/Images/marketing-resource/media/images/production/<EMAIL>" />
                        <h2>Azure Active Directory B2C</h2>
                        <h4>用于云的标识和访问管理</h4>
                    </div>
                </div>
            </div>
            <!-- END: Product-Detail-TopBanner -->
            <div class="pricing-page-section">
                <p>Azure Active Directory B2C 是一个基于云的标识和访问管理解决方案，适用于面向消费者的 Web 和移动应用程序。此服务在全球具有高可用性，可扩展到亿万消费者标识。以企业级安全平台为基础，Azure Active Directory B2C 可保护你的企业和消费者。</p>
            </div>
            <!-- BEGIN: TAB-CONTROL -->
            <div class="technical-azure-selector tab-control-selector">
                <h2>定价详细信息</h2>
                <p>Azure AD B2C 的定价将基于每月活跃用户数(MAU)，可帮助你降低成本并自信地进行预测。为外部用户提供免费层和灵活、可预测的定价:</p>
                <ul>
                    <li>免费更进一步：对于 Premium P1 和 Premium P2 功能，每月的前 50,000 位 MAU 都将免费。</li>
                    <li>灵活：根据所需使用量和功能（而不是你拥有的许可证）与客户和合作伙伴联系。</li>
                    <li>可预测：仅为所用部分付费。随着业务和使用量的增加，轻松进行预测。不就某位 MAU 的后续身份验证向客户收费，也不就该日历月中存储非活跃用户的行为收费。
                        Azure AD B2C 将按以下起始费率计费，包括企业协议客户。Premium P2 功能包括所有 Premium P1 功能以及市场领先的标识保护和标识治理控件，例如基于风险的条件访问策略和 Azure AD B2C 的标识保护报告</li>
                </ul>
                <p>Azure AD B2C 将按以下起始费率计费，包括企业协议客户。Premium P2 功能包括所有 Premium P1 功能以及市场领先的标识保护和标识治理控件，例如基于风险的条件访问策略和 Azure AD B2C 的标识保护报告。</p>
                
                <div class="tags-date">
                    <div class="ms-date">*以下价格均为含税价格。</div><br>
                    <div class="ms-date">*每月价格估算基于每个月 31 天的使用量。</div>
                </div>

     
                    <table cellpadding="0" cellspacing="0" id="aad-b2c-table-mau">                        
                   
                        <tr>
                            <th align="left" width="33%"><strong></strong></th>
                            <th align="left" width="33%"><strong>高级版P1

                            </strong></th>
                            <th align="left" width="33%"><strong>高级版P2

                            </strong></th>
                        </tr>
                        <tr>
                            <td>前 50,000 个 MAU</td>
                            <td>￥0/月度活跃用户</td>
                            <td>￥0/月度活跃用户</td>
                            <td rowspan="5"> </td>
                        </tr>
                        <tr>
                            <td>后 50,000 个 MAU</td>
                            <td>￥0.033/月度活跃用户
                          </td>
                            <td>￥0.165/月度活跃用户
                          </td>
                        </tr>
                        
                    </table>
                

                    <h4>单独收费:</h4>
                    <div class="tags-date">
                        <div class="ms-date">*以下价格均为含税价格。</div><br>
                        <div class="ms-date">*每月价格估算基于每个月 31 天的使用量。</div>
                    </div>
                    <table cellpadding="0" cellspacing="0" width="100%" id="aad-b2c-table-separate">
                        <tr>
                            <th align="left">基于MFA的SMS/电话事件</th>
                            <th align="left">&yen; 0.305 每个 SMS/电话事件</th>
                        </tr>
                    </table>
                    <p>我们仔细倾听了客户在可预测的、直观的和具有成本效益的客户标识解决方案计费方面的需求。基于 MAU 的计费可使客户受益:</p>
                    <ul>
                        <li><b>经过扩展的免费层:</b> 新的免费层通过每月涵盖客户的 50,000 名 MAU，扩大了范围 - 与之前 50,000 次身份验证的配额相比，每个唯一用户可进行身份验证的次数更多。大多数客户（他们每月的 MAU 数最多为 50,000 名）将能够免费使用 Azure AD B2C。</li>
                        <li><b>可预测的估算操作:</b> 与尝试预测每月身份验证的次数相比，根据 MAU 预估每月计费更简单且预测性更强。</li>
                        <li>
                            <b>基于使用量的计费:</b> 仅对你的 MAU 付费。不就某位 MAU 的后续身份验证向客户收费，也不就该日历月中存储非活动用户的行为收费。
                            <p>例如，如果你有 100,000 名月度活跃用户 (MAU) 使用标准功能，则无论每位 MAU 的身份验证次数如何，你的每月账单都是 ￥1650:</p>
                            <p>(50,000 名 MAU x &yen;0（免费层）) + (50,000 名 MAU x ￥0.033) = ￥1650</p>
                            <p>每个 MFA 尝试或 SMS/电话事件按 ￥0.305 计费</p>
                        </li>
                    </ul>


            </div>
            <!-- END: TAB-CONTROL -->
            <div class="pricing-page-section">
                <h2>支持和服务级别协议</h2>
                <p>如有任何疑问或需要帮助，请访问<a href="https://support.azure.cn/zh-cn/support/contact" id="inentity-contact-page">Azure 支持</a>选择自助服务或者其他任何方式联系我们获得支持。</p>
				 <p>Azure Active Directory 免费版不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问<a href="../../../support/sla/active-directory-b2c/" id="pricing_identity_sla">服务级别协议</a>页。</p>
            </div>
                <!--BEGIN: Support and service code chunk-->
                <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
                <!--END: Support and service code chunk-->
              
            <!--BEGIN: Support and service code chunk-->
            
             
                                                                             
            <!--END: Support and service code chunk-->                                                                                      
    
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->

<!-- BEGIN: Footer -->
<div class="public_footerpage"></div>
<!--END: Common sidebar-->


        
<link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet">
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="4dzSq3xeuKWVc6uR1tjmsjezpcygfV-IQn46j_rGQEl_If8ZwfDEnbVbx_vRG0S7iniIgfOvwYUKMJKlvcbx4G5gXMUu-j0Fu5JDu_cgLiM1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>

<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>

<!-- BEGIN: Minified RequireJs -->
<script src='../../../Static/Scripts/global.config.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/require.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>
<!-- END: Minified RequireJs -->

<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js"></script>

<script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
</script>
<!-- end JSLL -->

<script type="text/javascript" src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../Static/Scripts/wacndatatracker.js"></script>


<script type="text/javascript" src="/common/useCommon.js"></script>

</body>
</html>