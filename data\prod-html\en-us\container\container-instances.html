<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="azure container instances, container instances, container" name="keywords"/>
    <meta content="See pricing for Container Instances. Run containers on Azure with a single command and lower your infrastructure costs with per-second billing."
          name="description"/>
    <title>
        Pricing - Container Instances - Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/container-instances" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "12/9/2019 7:37:32 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "11/6/2019 2:43:10 AM";
    window.footerTimestamp = "11/6/2019 2:43:10 AM";
    window.locFileTimestamp = "11/6/2019 2:43:04 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-container-instances" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a, .pure-content .technical-azure-selector p a, .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        #container-instances-linux-table tr {
                            background-color: white;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/container-instances_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <!-- <img src="/Images/marketing-resource/media/images/production/<EMAIL>" /> -->
                                <span style="float: left;
                        margin-right: 10px;
                        margin-top: 5px;
                        width: 48px;">
           <svg data-slug-id="container-instances" viewbox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
            <defs>
             <lineargradient gradientunits="userSpaceOnUse" id="container-instances:f7543904-4a89-435e-96cd-11521c829faa-e5aeab6d" x1="9" x2="9" y1="11.95">
              <stop offset="0" stop-color="#0078d4">
              </stop>
              <stop offset="0.16" stop-color="#1380da">
              </stop>
              <stop offset="0.53" stop-color="#3c91e5">
              </stop>
              <stop offset="0.82" stop-color="#559cec">
              </stop>
              <stop offset="1" stop-color="#5ea0ef">
              </stop>
             </lineargradient>
            </defs>
            <path d="M17.43,8.21a3.78,3.78,0,0,0-3.29-3.64A4.77,4.77,0,0,0,9.22,0,4.91,4.91,0,0,0,4.54,3.19a4.52,4.52,0,0,0-4,4.35A4.6,4.6,0,0,0,5.32,12l.42,0h7.68l.21,0A3.84,3.84,0,0,0,17.43,8.21Z"
                  fill="url(#container-instances:f7543904-4a89-435e-96cd-11521c829faa-e5aeab6d)">
            </path>
            <path d="M6.36,6.46,9,3.87a.3.3,0,0,1,.43,0L12,6.46a.13.13,0,0,1-.1.23H10.28a.15.15,0,0,0-.14.14v3.24a.11.11,0,0,1-.11.11H8.29a.11.11,0,0,1-.11-.11V6.83a.14.14,0,0,0-.13-.14H6.45A.13.13,0,0,1,6.36,6.46Z"
                  fill="#f2f2f2">
            </path>
            <path d="M14,11.37a.13.13,0,0,0-.09-.13L9.16,9.65H9V18h.13l4.71-1.88A.13.13,0,0,0,14,16Z" fill="#a67af4">
            </path>
            <path d="M9,9.68l-4.51.83a.14.14,0,0,0-.12.13v6.23a.15.15,0,0,0,.11.14L9,18a.13.13,0,0,0,.16-.13v-8A.14.14,0,0,0,9,9.68Z" fill="#552f99">
            </path>
            <polygon fill="#b77af4" opacity="0.75" points="6.92 10.92 6.92 16.73 8.49 16.98 8.49 10.65 6.92 10.92">
            </polygon>
            <polygon fill="#b77af4" opacity="0.75" points="4.98 11.24 4.98 16.32 6.35 16.6 6.35 11.01 4.98 11.24">
            </polygon>
           </svg>
          </span>
                                <h2>
                                    Container Instances
                                    <span>
            Container Instances
           </span>
                                </h2>
                                <h4>
                                    Easily run containers on Azure without managing servers
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <p>
                            Azure Container Instances offers the fastest and simplest way to run a container in Azure, without having to provision any virtual machines or
                            learning new tools—it's just your application, in a container, running in the cloud. With Azure Container Instances, you can easily run
                            containers with a single command. Get started in seconds and lower your infrastructure costs by taking advantage of per second billing and
                            custom machine sizes.
                        </p>
                        <h2>
                            Container Instances pricing
                        </h2>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <!-- 手动控制不显示 -->
                                    <div class="dropdown-container software-kind-container" style="display: none">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Container Instances
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <!-- <li ><a href="javascript:void(0)" data-href="#tabContent1" id="home_container-instances-windows">Windows</a></li> -->
                                                <li class="active">
                                                    <a data-href="#tabContent2" href="javascript:void(0)" id="home_container-instances-linux">
                                                        Container Instances - Linux
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <!-- <option  data-href="#tabContent1" value="Windows">Windows</option> -->
                                            <option data-href="#tabContent2" selected="selected" value="Container Instances - Linux">
                                                Container Instances - Linux
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China North 3
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#north-china3" value="north-china3">
                                                China North 3
                                            </option>
                                            <option data-href="#east-china2" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content">
                            <!-- BEGIN: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 navigator 2 -->
                                <!-- BEGIN: Tab level 2 content 3 -->
                            </div>
                            <!-- END: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent2">
                                <!-- BEGIN: Tab level 2 navigator 2 -->
                                <!-- BEGIN: Tab level 2 content 3 -->
                                <div class="tab-content">
                                    <div class="scroll-table" style="display: block;">
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                *The following prices are tax-inclusive.
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *Monthly pricing estimates are based on 744 hours of usage per month.
                                            </div>
                                        </div>
                                        <table cellpadding="0" cellspacing="0" id="container-instances-linux-table-north3east2" width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        METER
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        PRICE
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td rowspan="2">
                                                    Container group duration
                                                </td>
                                                <td>
                                                    Memory: ￥0.24995 per GB-h
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    vCPU: ￥0.027467 per vCPU-h
                                                </td>
                                            </tr>
                                        </table>
                                        <p>
                                            <a href="https://docs.azure.cn/zh-cn/container-instances/container-instances-container-groups" style="font-size:16px;">
                                                Container group
                                            </a>
                                            duration is calculated from the time that we start to pull your first container's image (for a new deployment) or your
                                            container group is restarted (if already deployed), until the container group is stopped. For each container group, you can
                                            allocate a minimum of 1 vCPU and 1 GB, up to 16 GBs of memory to each vCPU. You can allocate up to 4 vCPU to each container
                                            group you deploy.
                                        </p>
                                        <p>
                                        </p>
                                    </div>
                                    <div class="scroll-table" style="display: block;">
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                *The following prices are tax-inclusive.
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *Monthly pricing estimates are based on 744 hours of usage per month.
                                            </div>
                                        </div>
                                        <table cellpadding="0" cellspacing="0" id="container-instances-linux-table-north2" width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        METER
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        PRICE
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td rowspan="2">
                                                    Container group duration
                                                </td>
                                                <td>
                                                    Memory: ￥0.045282 per GB-h
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    vCPU: ￥0.412128 per vCPU-h
                                                </td>
                                            </tr>
                                        </table>
                                        <p>
                                            <a href="https://docs.azure.cn/zh-cn/container-instances/container-instances-container-groups" style="font-size:16px;">
                                                Container group
                                            </a>
                                            duration is calculated from the time that we start to pull your first container's image (for a new deployment) or your
                                            container group is restarted (if already deployed), until the container group is stopped. For each container group, you can
                                            allocate a minimum of 1 vCPU and 1 GB, up to 16 GBs of memory to each vCPU. You can allocate up to 4 vCPU to each container
                                            group you deploy.
                                        </p>
                                        <p>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: TAB-CONTAINER-2 -->
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <h2>
                            Public IP addresses
                        </h2>
                        <p>
                            Public IP addresses assigned to your container group are billed at standard
                            <a href="/en-us/pricing/details/ip-addresses">
                                Azure rates
                            </a>
                            .
                        </p>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Pricing Example
                        </h2>
                        <h3>
                            Example 1:
                        </h3>
                        <p>
                            You create a Linux container group with a 1 vCPU, 1 GB configuration once daily during a month (30 days). The duration of each container group
                            is 5 hours.
                        </p>
                        <h3>
                            Memory duration:
                        </h3>
                        <p>
                            Number of container groups * memory duration (hours) * GB * price per GB-h * number of days
                        </p>
                        <p>
                            1 container group * 5 hours * 1 GB * ￥0.24995 per GB-h * 30 days = ￥37.4925
                        </p>
                        <h3>
                            vCPU duration:
                        </h3>
                        <p>
                            Number of container groups * vCPU duration (hours) * vCPU(hours) * price per vCPU-h * number of days
                        </p>
                        <p>
                            1 container groups * 5 hours * 1 vCPU * ￥0.027467 per vCPU-h * 30 days = ￥4.12005
                        </p>
                        <h3>
                            Total billing:
                        </h3>
                        <p>
                            Memory duration (seconds) + vCPU duration (seconds) = total cost
                        </p>
                        <p>
                            ￥37.4925+ ￥4.12005 = ￥41.61255
                        </p>
                        <h3>
                            Example 2:
                        </h3>
                        <p>
                            You create a Linux container group with a 1 vCPU, 2 GB configuration 50 times daily during a month (30 days).
                        </p>
                        <p>
                            The container group duration is 15 hours.
                        </p>
                        <h3>
                            Memory duration:
                        </h3>
                        <p>
                            Number of container groups * memory duration (hours) * GB * price per GB-h * number of days
                        </p>
                        <p>
                            50 container groups * 15 hours * 2 GB * ￥0.24995 per GB-h * 30 days = ￥374.925
                        </p>
                        <h3>
                            vCPU duration:
                        </h3>
                        <p>
                            Number of container groups * vCPU duration (hours) * vCPU(h) * price per vCPU-h * number of days
                        </p>
                        <p>
                            50 container groups * 15 hours * 1 vCPU * ￥0.027467 per vCPU-h * 30 days = ￥618.0075
                        </p>
                        <h3>
                            Total billing:
                        </h3>
                        <p>
                            Memory duration (hours) + vCPU duration (hours) = total cost
                        </p>
                        <p>
                            ￥374.925 + ￥618.0075 = ￥992.9325
                        </p>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="container-instances-ssis-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            To learn more about the details of the Service Level Agreement, please visit the
                            <a href="https://www.azure.cn/en-us/support/sla/container-instances/v1_0/index.html">
                                Service Level Agreements page
                            </a>
                            .
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static//Scripts/lib/jquery-1.12.3.min.js">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="eIeiJHFNARubIGYWCogeqCY0trboQueJgENM81mKo_L7SB4jkEtysdzvbvUhn8W1wLGabf8D24rylW8zO5F0H0SXn2DeYdc4T0vgwt0ADO01" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
