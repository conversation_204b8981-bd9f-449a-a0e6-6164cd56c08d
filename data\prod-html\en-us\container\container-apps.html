<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="azure container instances, container instances, container" name="keywords"/>
    <meta content="See pricing for Container Instances. Run containers on Azure with a single command and lower your infrastructure costs with per-second billing."
          name="description"/>
    <title>
        Pricing - Container Instances - Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/container-instances" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "12/9/2019 7:37:32 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "11/6/2019 2:43:10 AM";
    window.footerTimestamp = "11/6/2019 2:43:10 AM";
    window.locFileTimestamp = "11/6/2019 2:43:04 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-container-instances" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a,
                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        #container-instances-linux-table tr {
                            background-color: white;
                        }
                    </style>
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/container-instances_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <!-- <img src="/Images/marketing-resource/media/images/production/<EMAIL>" /> -->
                                <span style="float: left;
                                    margin-right: 10px;
                                    margin-top: 5px;
                                    width: 48px;
                                    height: 48px;
                                    background: url('/Images/aca.svg') center no-repeat; background-size: contain;">
           "&gt;
          </span>
                                <h2>
                                    Azure Container Apps
                                    <span>
            Azure Container Apps
           </span>
                                </h2>
                                <h4>
                                    Build and deploy modern apps and microservices using serverless containers
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Explore pricing options
                        </h2>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <!-- 手动控制不显示 -->
                                    <div class="dropdown-container software-kind-container" style="display: none">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Container Instances
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <!-- <li ><a href="javascript:void(0)" data-href="#tabContent1" id="home_container-instances-windows">Windows</a></li> -->
                                                <li class="active">
                                                    <a data-href="#tabContent2" href="javascript:void(0)" id="home_container-instances-linux">
                                                        Container Instances -
                                                        Linux
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <!-- <option  data-href="#tabContent1" value="Windows">Windows</option> -->
                                            <option data-href="#tabContent2" selected="selected" value="Container Instances - Linux">
                                                Container Instances - Linux
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                            <span class="selected-item">
                                                China North 3
                                            </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                                                        China East 3
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#east-china3" value="east-china3">
                                                China East 3
                                            </option>
                                            <option data-href="#north-china3" value="north-china3">
                                                China North 3
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: TAB-CONTAINER-2 -->
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <h2>
                                Consumption plan
                            </h2>
                            <p>
                                Azure Container Apps consumption plan is billed based on per-second resource
                                allocation
                                and requests. The first 180,000 vCPU-seconds, 360,000 GiB-seconds, and 2 million
                                requests each month are free. Beyond that, you pay for what you use on a per second
                                basis based on the number of vCPU-s and GiB-s your applications are allocated.
                            </p>
                            <p>
                                Applications scale on-demand based on requests and events. Container Apps replicas
                                are
                                billed for active usage when they are running. An application can be configured to
                                scale
                                to zero replicas when there are no requests or events to process. No usage charges
                                apply
                                when an application is scaled to zero.
                            </p>
                            <p>
                                You can optionally configure Container Apps with a minimum number of replicas to be
                                always running in idle mode. When an application scales down to its minimum number
                                of
                                replicas, usage is charged at a reduced idle rate when a replica is inactive. A
                                replica
                                enters active mode and is charged at the active rate when it is starting up, when it
                                is
                                processing requests, or when its vCPU or bandwidth usage are above the active
                                billing
                                thresholds1.
                            </p>
                            <p>
                                For Container Apps jobs, resources consumed by each execution are billed at the
                                active
                                rate from its start to completion. No usage charges apply when a job is not running
                                executions.
                            </p>
                        </div>
                        <style>
                            #containerapps .marginno {
                                margin-top: -80px;
                            }

                            #containerapps .margin0 {
                                margin-top: 0px;
                            }
                        </style>
                        <div class="pricing-page-section margin0">
                            <h2>
                                Resource consumption
                            </h2>
                            <!-- <p>1 A replica is active when vCPU usage is above 0.01 cores or when data received is
                                                       above
                                                       1,000 bytes per second.</p> -->
                        </div>
                        <div class="pricing-page-section">
                            <table cellpadding="0" cellspacing="0" class="data-table__table data-table__table--pricing" width="100%">
                                <thead>
                                <tr style="text-align: left;">
                                    <th>
                                        <b>
                                            Meter
                                        </b>
                                    </th>
                                    <th>
                                        <b>
                                            Active Usage Price
                                        </b>
                                    </th>
                                    <th>
                                        <b>
                                            Idle Usage Price
                                        </b>
                                    </th>
                                    <th>
                                        <b>
                                            Free Grant (Per Month)
                                        </b>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="data-table__stripe">
                                    <td>
                                        vCPU (seconds)
                                    </td>
                                    <td>
                                        ￥0.00024 per second
                                    </td>
                                    <td>
                                        ￥0.00003 per second
                                    </td>
                                    <td>
                                        180,000 vCPU-seconds
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Memory (GiB-Seconds)
                                    </td>
                                    <td>
                                        ￥0.000031 per second
                                    </td>
                                    <td>
                                        ￥0.000031 per second
                                    </td>
                                    <td>
                                        360,000 GiB-seconds
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <p style="font-size: 12px;">
                                1 A replica is active when vCPU usage is above 0.01 cores or
                                when data received is above 1,000 bytes per second.
                            </p>
                        </div>
                        <div class="pricing-page-section margin0">
                            <h2>
                                Requests
                            </h2>
                            <p>
                                Container Apps are billed based on the total number of requests 2 processed each
                                month.
                                The first two million requests are included free each month.
                            </p>
                        </div>
                        <div class="pricing-page-section">
                            <table cellpadding="0" cellspacing="0" class="data-table__table data-table__table--pricing" width="100%">
                                <thead>
                                <tr style="text-align: left;">
                                    <th>
                                        <b>
                                            Meter
                                        </b>
                                    </th>
                                    <th>
                                        <b>
                                            Price
                                        </b>
                                    </th>
                                    <th>
                                        <b>
                                            Free Grant (Per Month)
                                        </b>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="data-table__stripe">
                                    <td style="width: 33.3%;">
                                        Requests
                                    </td>
                                    <td style="width: 33.3%;">
                                        ¥ 2.54 per million
                                    </td>
                                    <td>
                                        2 Million
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pricing-page-section margin0">
                            <h2>
                                Dedicated plan
                            </h2>
                            <p>
                                Azure Container Apps Dedicated plan provides a single tenancy guarantee, access to
                                specialized hardware, and more predictable pricing. Billing for the Dedicated plan
                                is
                                based on the number of vCPU seconds and gibibyte (GiB) seconds allocated across
                                instances. In addition, you are billed a base price for the Dedicated plan
                                management.
                            </p>
                            <p>
                                When creating apps in the Dedicated plan you can choose from several workload
                                profiles,
                                each with different amounts of vCPUs and GiBs of memory. You will be billed for the
                                total number of vCPUs and memory provisioned in this workload profile, per second
                                that
                                each instance is running. More than one app can run in a single workload profile
                                depending on how many resources each app requires and the resources available in the
                                workload profile. These workload profiles can automatically scale out to multiple
                                instances as needed. To learn more, see Azure Container Apps Dedicated plan pricing
                                details.
                            </p>
                        </div>
                        <div class="pricing-page-section">
                            <table cellpadding="0" cellspacing="0" class="data-table__table data-table__table--pricing" width="100%">
                                <thead>
                                <tr style="text-align: left;">
                                    <th>
                                        <b>
                                            Meter
                                        </b>
                                    </th>
                                    <th>
                                        <b>
                                            Price
                                        </b>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="data-table__stripe">
                                    <td>
                                        Dedicated plan management (hour)
                                    </td>
                                    <td>
                                        ¥ 0.636 per hour
                                    </td>
                                </tr>
                                <tr class="data-table__stripe">
                                    <td>
                                        vCPU (hour)
                                    </td>
                                    <td>
                                        ¥ 0.575 per hour
                                    </td>
                                </tr>
                                <tr class="data-table__stripe">
                                    <td>
                                        Memory (GiB-hour)
                                    </td>
                                    <td>
                                        ¥ 0.053 per hour
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
    <script src="/Static//Scripts/lib/jquery-1.12.3.min.js">
    </script>
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="eIeiJHFNARubIGYWCogeqCY0trboQueJgENM81mKo_L7SB4jkEtysdzvbvUhn8W1wLGabf8D24rylW8zO5F0H0SXn2DeYdc4T0vgwt0ADO01" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="/Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</div>
</body>
</html>
