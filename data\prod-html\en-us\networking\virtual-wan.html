<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="virtual wan, virtual wide area network, cloud service providers" name="keywords"/>
    <meta content="Azure Virtual WAN is a networking service providing optimized and automated branch to branch connectivity through Azure." name="description"/>
    <title>
        Pricing - Virtual WAN - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/virtual-wan/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
                        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-virtual-wan" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/virtual-wan-banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Virtual WAN
                                    <span>
                                    Virtual WAN
                                    </span>
                                </h2>
                                <h4>
                                    Simple, unified, global connectivity and security
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure Virtual WAN is a networking service providing optimized and automated branch to branch connectivity through Azure. Virtual WAN allows
                            customers to connect branches to each other and Azure, centralizing their network and security needs with virtual appliances such as firewalls
                            and Azure network and security services.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <h2>
                            Pricing overview
                        </h2>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly price estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <thead>
                                <tr>
                                    <th align="left">
                                        <strong>
                                            TYPE
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            PRICE
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            UNIT
                                        </strong>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="left_align">
                                        Standard Virtual WAN Hub
                                    </td>
                                    <td class="left_align">
                                        ¥ 2.544/hour
                                    </td>
                                    <td class="left_align">
                                        1 per Deployment Hour
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Standard Virtual WAN Hub with Route maps or a Firewall Manager Policy for 3rd Party Integrations
                                    </td>
                                    <td class="left_align">
                                        ¥ 4.068/hour
                                    </td>
                                    <td class="left_align">
                                        1 per Deployment Hour
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Standard Virtual WAN Hub Data Processing
                                    </td>
                                    <td class="left_align">
                                        ¥ 0.2034/GB
                                    </td>
                                    <td class="left_align">
                                        Per GB
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Firewall NVA Data Processing
                                    </td>
                                    <td class="left_align">
                                        ¥ 0.05088/GB
                                    </td>
                                    <td class="left_align">
                                        Per GB
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        VPN S2S Scale Unit
                                        <sup>
                                            1
                                        </sup>
                                    </td>
                                    <td class="left_align">
                                        ¥ 1.844/hour
                                    </td>
                                    <td class="left_align">
                                        500 Mbps per Scale Unit
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        VPN S2S Connection Unit
                                        <sup>
                                            2
                                        </sup>
                                    </td>
                                    <td class="left_align">
                                        ¥ 0.814/hour
                                    </td>
                                    <td class="left_align">
                                        1 Connection
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        VPN P2S Scale Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 3.674/hour
                                    </td>
                                    <td class="left_align">
                                        500 Mbps per Scale Unit
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        VPN P2S Connection Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 0.127/hour
                                    </td>
                                    <td class="left_align">
                                        1 Connection
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        ExpressRoute Scale Unit
                                        <sup>
                                            3
                                        </sup>
                                    </td>
                                    <td class="left_align">
                                        ¥ 2.544/hour
                                    </td>
                                    <td class="left_align">
                                        2 Gbps per Scale Unit
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        ExpressRoute Connection Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 0.509/hour
                                    </td>
                                    <td class="left_align">
                                        1 Connection
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        ExpressRoute Connection Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 0.509/hour
                                    </td>
                                    <td class="left_align">
                                        1 Connection
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        NVA Infrastructure Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 2.544/hour
                                    </td>
                                    <td class="left_align">
                                        1 Unit
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Routing Infrastructure Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 1.02/hour
                                    </td>
                                    <td class="left_align">
                                        1 Unit
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="tags-date">
                                <div class="ms-date">
                                    <sup>
                                        1
                                    </sup>
                                    VPN S2S Scale Unit is the aggregate speed of all branch site connections to the hub and is scalable up to 20 Gbps.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    <sup>
                                        2
                                    </sup>
                                    VPN S2S Connection Unit represents the amount of branch sites connected to a hub and supports a maximum of 1,000 connections per Azure
                                    region.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    <sup>
                                        3
                                    </sup>
                                    Scale unit 6 to 10 are charged in an increment of ¥1.514/hour.
                                </div>
                            </div>
                            <!-- END: Table1-Content-->
                        </div>
                        <h2>
                            Azure Firewall with Secured Virtual Hub Pricing
                        </h2>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <thead>
                                <tr>
                                    <th align="left">
                                        <strong>
                                            SKU
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Type
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Price
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Unit
                                        </strong>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="left_align" rowspan="2">
                                        Standard
                                    </td>
                                    <td class="left_align">
                                        Secured Virtual Hubs Deployment Unit
                                    </td>
                                    <td class="left_align">
                                        ￥12.72/hour
                                    </td>
                                    <td class="left_align">
                                        1 Per deployment hour
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Secured Virtual Hubs Data Processed Unit
                                    </td>
                                    <td class="left_align">
                                        ￥0.1632/GB
                                    </td>
                                    <td class="left_align">
                                        1 Per GB processed
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align" rowspan="2">
                                        Premium
                                    </td>
                                    <td class="left_align">
                                        Secured Virtual Hubs Deployment Unit
                                    </td>
                                    <td class="left_align">
                                        ¥ 17.808/hour
                                    </td>
                                    <td class="left_align">
                                        1 Per deployment hour
                                    </td>
                                </tr>
                                <tr>
                                    <td class="left_align">
                                        Secured Virtual Hubs Data Processed Unit
                                    </td>
                                    <td class="left_align">
                                        ￥0.1632/GB
                                    </td>
                                    <td class="left_align">
                                        1 Per GB processed
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="tags-date">
                                <div class="ms-date">
                                    <p>
                                        More details about Azure Firewall pricing can be found
                                        <a href="https://www.azure.cn/en-us/pricing/details/azure-firewall/">
                                            here.
                                        </a>
                                    </p>
                                </div>
                                <br/>
                            </div>
                            <!-- END: TAB-CONTAINER-1 -->
                        </div>
                        </div>
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <div class="more-detail">
                                <h2>
                                    FAQ
                                </h2>
                                <em>
                                    Expand All
                                </em>
                                <ul>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="dns-que-q1">
                                                Is a partial hour billed as a full hour?
                                            </a>
                                            <section>
                                                <p>
                                                    Yes. A partial hour is billed as a full hour.
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="dns-que-q2">
                                                Are data transfers over the VPN connection charged separately?
                                            </a>
                                            <section>
                                                <p>
                                                    Data transfers over the VPN connections to your on-premises sites or the internet in general are charged
                                                    separately at the regular data transfer rate.
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="vpn-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee at least 99.9% availability of the basic gateway of each VPN or ExpressRoute, 99.95% availability of the
                            standard gateway of each VPN or ExpressRoute, and 99.95% availability of the high-performance gateway of each VPN or
                            ExpressRoute. To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/vpn-gateway/index.html" id="vpn_authentication_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="3uUVzDEHX0jq_JZ5kWa4PUDfC2aLtd2sQ23veJtW6RN4n_veT1vQA6NKgOFIxCh7yWtQmWqvcGX6xsM3B2s7j3LczLHHRP19iUHeHWnfWpo1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
