<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="Azure 微软云,Azure Cosmos DB, 价格详情定价计费" name="keywords" />
    <meta
        content="了解Azure Cosmos DB 的价格详情。Azure Cosmos DB 根据存储的数据量（以 GB 计）和保留吞吐量（以 100 个 RU/每秒的单位计），按小时对每个集合计费；还对跨区域数据传输如何进行收费进行了详细说明。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。"
        name="description" />
    <title>
        Azure Cosmos DB NoSQL 数据库服务定价 - Azure 云计算
    </title>
    <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/cosmos-db/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <style type="text/css">
        .pricing-detail-tab .tab-nav {
            padding-left: 0 !important;
            margin-top: 5px;
            margin-bottom: 0;
            overflow: hidden;
        }

        .pricing-detail-tab .tab-nav li {
            list-style: none;
            float: left;
        }

        .pricing-detail-tab .tab-nav li.active a {
            border-bottom: 4px solid #00a3d9;
        }

        .pricing-detail-tab .tab-nav li.active a:hover {
            border-bottom: 4px solid #00a3d9;
        }

        .pricing-detail-tab .tab-content .tab-panel {
            display: none;
        }

        .pricing-detail-tab .tab-content .tab-panel.show-md {
            display: block;
        }

        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
            padding-left: 5px;
            padding-right: 5px;
            color: #00a3d9;
            background-color: #FFF;
        }

        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
            color: #FFF;
            background-color: #00a3d9;
        }

        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
            color: #FFF;
            background-color: #00a3d9;
        }

        .pure-content .technical-azure-selector .tags-date a,
        .pure-content .technical-azure-selector p a,
        .pure-content .technical-azure-selector table a {
            background: 0 0;
            padding: 0;
            margin: 0 6px;
            height: 21px;
            line-height: 22px;
            font-size: 14px;
            color: #00a3d9;
            float: none;
            display: inline;
        }

         .updatetime {
            color: black;
            text-align: right;
            font-size: 12px;
        }
    </style>
    <div class="hide-info" style="display:none;">
        <div class="bg-box">
            <div class="cover-bg">
            </div>
        </div>
        <div class="msg-box">
            <div class="pricing-unavailable-message">
                所选区域不可用
            </div>
        </div>
    </div>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="cosmos-db" wacn.date="11/27/2015">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/cosmos-db_banner.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Images/marketing-resource/css/<EMAIL>" />
                                    <h2>
                                        Azure Cosmos DB定价
                                    </h2>
                                    <h4>
                                        适用于任何规模的带有开放 API 的快速 NoSQL 数据库
                                    </h4>
                                    <div class = "updatetime">更新时间：2025年08月14日</div>  
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <p>
                                完全托管的 NoSQL 数据库服务
                                <a href="/support/sla/cosmos-db/index.html">
                                    保证了速度和可用性
                                </a>
                                ，且提供自动即时可伸缩性以及开源 API，可用于快速、轻松地开发任意规模的应用。Azure Cosmos DB 为从开发/测试到生产等任何规模的应用提供具有成本效益的定价模型。
                            </p>
                            <h2>Azure Cosmos DB 定价模型</h2>
                            <p> Cosmos DB 针对三种不同类型的使用计费: 计算、存储和带宽。下面的选项卡更详细地描述了每个计算定价模型，及该模型随附的存储和带宽定价模型。选择计算定价模式和
                                API 后，将无法更改它们。</p>
                            <h2>计算定价:</h2>
                            <p><strong>请求单位(吞吐量):</strong> Azure Cosmos DB 使用每秒测量的请求单位(RU/s)进行计费。请求单位代表用于处理数据库操作的计算、内存和
                                IO，并在你的 Azure
                                Cosmos DB 帐户的所有选定 Azure 区域中计费。</p>
                            <p>吞吐量选项包括: 标准预配吞吐量、自动缩放预配吞吐量和无服务器。</p>
                            <p>适用的 API: NoSQL、MongoDB (RU)、Cassandra、Gremlin 和 Table。</p>
                            <p><strong>vCore:</strong> Azure Cosmos DB 对每个节点中用于处理数据库操作的 vCore (计算和内存)计费，计费依据为预配节点的大小和数目。
                            </p>
                            <p>可用的 API: PostgreSQL 和 MongoDB (vCore)。 </p>
                            <h2>存储定价:</h2>
                            <p>已使用存储: Azure Cosmos DB 按每区域每容器/集合/表/图对已使用存储计费，结果舍入到下一 GB。已使用存储包括所有事务和分析数据与索引以及备份。</p>
                            <p>适用的 API: NoSQL、MongoDB (RU)、Cassandra、Gremlin 和 Table。 </p>
                            <p><strong>磁盘存储:</strong> Azure Cosmos DB 对每个节点所预配的磁盘按存储大小计费。</p>
                            <p>适用的 API: PostgreSQL 和 MongoDB (vCore)。</p>
                            <h2>带宽定价:</h2>
                            <p><strong>数据出口:</strong> Azure 对从 Azure 云传出的数据，或者跨区域或可用性区域穿过 Azure WAN 的数据计费。</p>
                            <p>适用的 API: 全部。</p>
                        </div>
                        <!-- BEGIN: TAB-CONTROL -->
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                            <div class="tab-container-container">
                                <div class="tab-container-box">
                                    <div class="tab-container">
                                        <div class="dropdown-container software-kind-container" style="display:none;">
                                            <label>
                                                OS/软件:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    Azure Cosmos DB
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#tabContent1" href="javascript:void(0)"
                                                            id="home_storage-blobs">
                                                            Azure Cosmos DB
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                                <option data-href="#tabContent1" selected="selected"
                                                    value="Azure Cosmos DB">
                                                    Azure Cosmos DB
                                                </option>
                                            </select>
                                        </div>
                                        <div class="dropdown-container region-container">
                                            <label>
                                                地区:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    中国东部 3
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#east-china3" href="javascript:void(0)"
                                                            id="east-china3">
                                                            中国东部 3
                                                        </a>
                                                    </li>
                                                    <li class="active">
                                                        <a data-href="#north-china3" href="javascript:void(0)"
                                                            id="north-china3">
                                                            中国北部 3
                                                        </a>
                                                    </li>
                                                    <li class="active">
                                                        <a data-href="#east-china2" href="javascript:void(0)"
                                                            id="east-china2">
                                                            中国东部 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china2" href="javascript:void(0)"
                                                            id="north-china2">
                                                            中国北部 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china" href="javascript:void(0)"
                                                            id="east-china">
                                                            中国东部
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china" href="javascript:void(0)"
                                                            id="north-china">
                                                            中国北部
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                                <option data-href="#east-china3" value="north-china3">
                                                    中国东部3
                                                </option>
                                                <option data-href="#north-china3" value="north-china3">
                                                    中国北部3
                                                </option>
                                                <option data-href="#east-china2" selected="selected"
                                                    value="east-china2">
                                                    中国东部 2
                                                </option>
                                                <option data-href="#north-china2" value="north-china2">
                                                    中国北部 2
                                                </option>
                                                <option data-href="#east-china" value="east-china">
                                                    中国东部
                                                </option>
                                                <option data-href="#north-china" value="north-china">
                                                    中国北部
                                                </option>
                                            </select>
                                        </div>
                                        <div class="clearfix">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-content">
                                <div class="tab-panel" id="tabContent1">
                                    <div class="category-container-container">
                                        <div class="category-container-box">
                                            <div class="category-container">
                                                <span class="category-title hidden-lg hidden-md">
                                                    类别：
                                                </span>
                                                <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
                                                    <li class="active">
                                                        <a data-href="#tabContent1-1" href="javascript:void(0)"
                                                            id="home_storage_gpvhy">
                                                            标准预配吞吐量
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#tabContent1-2" href="javascript:void(0)"
                                                            id="home_storage_gpv2">
                                                            自动缩放预配吞吐量
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#tabContent1-3" href="javascript:void(0)"
                                                            id="home_storage_blobs">
                                                            无服务器
                                                        </a>
                                                    </li>
                                                </ul>
                                                <select class="dropdown-select category-tabs hidden-lg hidden-md">
                                                    <option data-href="#tabContent1-1" id="home_storage_gpv2"
                                                        value="General Purpose v2">
                                                        标准预配吞吐量
                                                    </option>
                                                    <option data-href="#tabContent1-2" id="home_storage_blobs"
                                                        value="Blob storage">
                                                        自动缩放预配吞吐量
                                                    </option>
                                                    <option data-href="#tabContent1-3" id="home_storage_gpv2"
                                                        value="General Purpose V2 Hierarchical Namspace">
                                                        无服务器
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-content">
                                        <div class="tab-panel" id="tabContent1-1">
                                            <h3>
                                                预配的吞吐量
                                            </h3>
                                            <p>
                                                预配吞吐量在 SLA 的支持下，保证读取和写入延迟低至个位数毫秒级且全球可用性达
                                                99.999%。它非常适合需要保证低延迟和高可用性的大型关键工作负载。通过
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/optimize-dev-test#azure-cosmos-db-free-tier"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    Azure Cosmos DB 免费层
                                                </a>
                                                ，新帐户有资格获取每月 1000 个请求单位/秒 (RU/s) 的吞吐量和 25 GB 的存储空间。
                                            </p>
                                            <p>
                                                预配的吞吐量有两个容量管理选项：自动缩放预配吞吐量和标准预配吞吐量。选择哪一种取决于工作负荷的可预测性，以及是否希望手动管理容量。
                                            </p>
                                            <br />
                                            <h3>
                                                标准（手动）预配吞吐量
                                            </h3>
                                            <p>
                                                使用标准预配吞吐量直接管理容量。此选项适用于其流量模式可预测的大型关键工作负载。
                                            </p>
                                            <p>
                                                最低为每秒 400 个请求单位 (RU/s)，可以使用
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/set-throughput#set-throughput-on-a-database-and-a-container"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    Azure 门户
                                                </a>
                                                或
                                                <a href="https://docs.microsoft.com/zh-cn/dotnet/api/microsoft.azure.cosmos.container.replacethroughputasync?view=azure-dotnet#Microsoft_Azure_Cosmos_Container_ReplaceThroughputAsync_System_Int32_Microsoft_Azure_Cosmos_RequestOptions_System_Threading_CancellationToken"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    以编程方式使用 API
                                                </a>
                                                在容器或数据库上手动配置标准预配的吞吐量。将针对你的容器或数据库上预配的 RU/s 按小时计费。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            标准预配吞吐量
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每小时总 RU/s
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 100 RU/秒的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        单区域写入帐户
                                                    </td>
                                                    <td>
                                                        100 RU/s x 1 个区域
                                                    </td>
                                                    <td>
                                                        ￥0.051/小时
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        跨 N 个区域分布的单区域写入帐户
                                                    </td>
                                                    <td>
                                                        100 RU/s x N 个区域
                                                    </td>
                                                    <td>
                                                        ￥0.051/小时
                                                    </td>
                                                </tr>
                                                <!-- <tr>
                                    <td>单区域写入帐户，具有使用可用性区域的区域</td>
                                    <td>100 RU/s x 1.25 x N 个区域</td>
                                    <td>￥0.051/小时</td>
                                </tr> -->
                                                <tr>
                                                    <td>
                                                        跨 N 个区域的多区域写入（以前称为“多主数据库”）帐户
                                                    </td>
                                                    <td>
                                                        100 RU/s x N 个区域
                                                    </td>
                                                    <td>
                                                        ￥0.102/小时
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-s-n3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            标准配置吞吐量
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每小时总RU/s
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            单价 100 RU/s
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        单区域写入帐户，区域使用可用性区域
                                                    </td>
                                                    <td>
                                                        100 RU/S X 1.25 X N区域
                                                    </td>
                                                    <td>
                                                        ￥0.051/小时
                                                    </td>
                                                </tr>
                                            </table>
                                            <div class="tags-date">
                                                <div class="ms-date">
                                                    收取标准数据传输费率。
                                                </div>
                                                <br />
                                                <div class="ms-date">
                                                    必须为 Azure Cosmos DB 容器和数据库预配至少 400 RU/s 的吞吐量。
                                                </div>
                                                <br />
                                                <div class="ms-date">
                                                    *请参阅下面的“可用性区域”部分了解详细信息。
                                                </div>
                                                <br />
                                                <div class="ms-date">
                                                    可以使用
                                                    <a href="https://cosmos.azure.com/capacitycalculator/"
                                                        style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                        Azure Cosmos DB 容量规划器（RU 计算器）
                                                    </a>
                                                    来估计预配吞吐量需求
                                                </div>
                                            </div>
                                            <br />
                                            <h3>
                                                使用的存储
                                            </h3>
                                            <p>
                                                Azure Cosmos DB 提供不受限制的事务和分析存储，该存储按数据和索引在为异地复制选择的所有区域中使用的 SSD 支持的逻辑存储量（以
                                                GB 为单位）计费。例如，如果跨三个区域复制 Azure Cosmos DB 帐户，则将为这三个区域中的每个区域支付总存储成本。
                                            </p>
                                            <p>
                                                你的数据在两个不同的存储层（事务和分析）中进行管理，工作负载在相同的逻辑数据上运行，彼此之间互不干扰。虽然事务存储默认为始终启用，但必须在
                                                Azure Cosmos DB 容器上显式启用分析存储，以便使用
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/synapse-link"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    Azure Synapse Link
                                                </a>
                                                对 Azure Cosmos DB 中的数据运行分析。
                                            </p>
                                            <p>
                                                预配的吞吐量 (RU/s) 根据事务性存储量的大小按每 GB 存储量 10 RU/s 的速率缩放。要估算存储需求，请使用
                                                <a href="https://cosmos.azure.com/capacitycalculator/"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    容量规划器工具
                                                </a>
                                                ，并确保你已预配了足够的吞吐量来满足存储需求。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            使用的存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        事务存储（行导向）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 2.576/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        分析存储（列导向）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 0.149/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-3-n3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            使用的存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格 GB
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        事务存储（面向行）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 2.576/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <br />
                                            <h3>
                                                备份存储空间
                                            </h3>
                                            <p>
                                                帐户可以选择定期备份或连续备份。默认情况下，将在所有帐户上激活定期备份，并免费存储数据的两个备份副本。定期数据备份可以配置为地域、本地或区域可复原。有关详细信息，请参阅技术文档。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            定期备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            LRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            ZRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            RA-GRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        存储的定期备份数据（2 个副本）
                                                    </td>
                                                    <td>
                                                        每个副本的 GB 数
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        存储的定期备份数据（&gt;2 个副本)
                                                    </td>
                                                    <td>
                                                        每个副本的 GB 数
                                                    </td>
                                                    <td>
                                                        ￥0.786/月
                                                    </td>
                                                    <td>
                                                        ￥0.978/月
                                                    </td>
                                                    <td>
                                                        ￥1.566/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <p>
                                                可以使用 Core (SQL) API 或适用于 MongoDB 的 API
                                                在帐户上激活连续备份（预览版），而不使用定期备份。激活后，将根据所有选定的 Azure
                                                区域中存储的数据总量按月收取备份费用。从连续备份数据进行时间点还原，按还原到主要写入区域的数据的总 GB 数进行计费。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            连续备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 7 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 30 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        ￥2.03/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        时间点还原
                                                    </td>
                                                    <td>
                                                        GB
                                                    </td>
                                                    <td>
                                                        ￥1.524/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-11-1" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            连续备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 7 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 30 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        ￥2.03/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        时间点还原
                                                    </td>
                                                    <td>
                                                        GB
                                                    </td>
                                                    <td>
                                                        ￥1.02/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <div class="scroll-table" style="display: block;">
                                                <h3>
                                                    分析存储事务
                                                </h3>
                                                <p>
                                                    分析存储的 IO（输入/输出）事务按操作数计费。
                                                </p>
                                                <table cellpadding="0" cellspacing="0" id="cosmos-4" width="100%">
                                                    <tr>
                                                        <th align="left">
                                                            <strong>
                                                                事务
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                操作
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                价格
                                                            </strong>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            写入操作 - 分析存储
                                                        </td>
                                                        <td>
                                                            10,000
                                                        </td>
                                                        <td>
                                                            ￥0.045
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            读取操作 - 分析存储
                                                        </td>
                                                        <td>
                                                            10,000
                                                        </td>
                                                        <td>
                                                            ￥0.015
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <h3>带宽</h3>
                                            <p>Azure Cosmos DB 对从 Azure 云流出到 Internet 上的目的地或在区域之间传输 Azure WAN 的数据计费。</p>
                                            <h3>数据传入(流入量)</h3>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th align="left" style="width: 700px;">

                                                    </th>
                                                    <th align="left">
                                                        价格
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>到任何区域的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                                <tr>
                                                    <td>任何区域内的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                                <tr>
                                                    <td>可用性区域内或任何区域内的可用性区域之间的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                            </table>
                                            <h3>数据传出(流出量)</h3>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th  align="left"></th>
                                                    <th  align="left">前5GB/月</th>
                                                    <th  align="left">价格</th>
                                                </tr>
                                                <tr>
                                                    <td>到中国区域1 的 数据传输</td>
                                                    <td>免费</td>
                                                    <td>￥0.408/GB</td>
                                                </tr>
                                            </table>
                                            <div class="tags-date">
                                                <div class="ms-date">
                                                    如果需要更详细地估算工作负荷所需的分析存储操作，请使用
                                                    <a href="https://cosmos.azure.com/capacitycalculator/"
                                                        style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                        容量规划器工具
                                                    </a>
                                                    并参阅
                                                    <a href="https://docs.azure.cn/zh-cn/cosmos-db/analytical-store-introduction#analytical-store-pricing"
                                                        style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                        Azure Cosmos DB 分析存储定价文档
                                                    </a>
                                                    ，了解更多详细信息。
                                                </div>
                                                <br />
                                                <div class="ms-date">
                                                    注意：事务存储没有单独的 IO 费用，因为这是 RU 的一部分。
                                                </div>
                                            </div>
                                            <br />
                                            <h3>
                                                多个区域
                                            </h3>
                                            <p>
                                                Azure Cosmos DB 通过多区域读取和
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/global-dist-under-the-hood"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    多区域写入
                                                </a>
                                                （以前称为“多主数据库”），跨所有 Azure 区域提供有保证的低延迟和高可用性。可随时向 Azure Cosmos DB 帐户
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/distribute-data-globally"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    添加区域或从中删除区域
                                                </a>
                                                。多个区域的计费影响在于，预配的吞吐量和使用的存储将乘以与你的帐户关联的区域数。
                                            </p>
                                        </div>
                                        <div class="tab-panel" id="tabContent1-2">
                                            <h3>
                                                预配的吞吐量
                                            </h3>
                                            <p>
                                                预配吞吐量在 SLA 的支持下，保证读取和写入延迟低至个位数毫秒级且全球可用性达
                                                99.999%。它非常适合需要保证低延迟和高可用性的大型关键工作负载。通过
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/optimize-dev-test#azure-cosmos-db-free-tier"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    Azure Cosmos DB 免费层
                                                </a>
                                                ，新帐户有资格获取每月 1000 个请求单位/秒 (RU/s) 的吞吐量和 25 GB 的存储空间。
                                            </p>
                                            <p>
                                                预配的吞吐量有两个容量管理选项：自动缩放预配吞吐量和标准预配吞吐量。选择哪一种取决于工作负荷的可预测性，以及是否希望手动管理容量。
                                            </p>
                                            <br />
                                            <div class="scroll-table" style="display: block;">
                                                <h3>
                                                    自动缩放预配吞吐量
                                                </h3>
                                                <p>
                                                    无需手动管理大型关键工作负载的容量，在流量模式不可预测时可以获得成本效益。可使用
                                                    <a href="https://docs.azure.cn/zh-cn/cosmos-db/set-throughput#set-throughput-on-a-database-and-a-container"
                                                        style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                        Azure
                                                        门户
                                                    </a>
                                                    或
                                                    <a href="https://docs.microsoft.com/zh-cn/dotnet/api/microsoft.azure.cosmos.container.replacethroughputasync?view=azure-dotnet#Microsoft_Azure_Cosmos_Container_ReplaceThroughputAsync_System_Int32_Microsoft_Azure_Cosmos_RequestOptions_System_Threading_CancellationToken"
                                                        style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                        以编程方式使用
                                                        API
                                                    </a>
                                                    设置自定义吞吐量限制（最低为 1,000 RU/s）。根据每小时使用的每秒最大请求单位数 (RU/s) 计费，该值介于吞吐量限制的 10
                                                    - 100%。
                                                </p>
                                                <table cellpadding="0" cellspacing="0" id="cosmos-5" width="100%">
                                                    <tbody>
                                                        <tr>
                                                            <th align="left">
                                                                <strong>
                                                                    标准预配吞吐量
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    每小时总 RU/s
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    每 100 RU/秒的价格
                                                                </strong>
                                                            </th>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                单区域写入帐户
                                                            </td>
                                                            <td>
                                                                100 RU/s x 1 个区域
                                                            </td>
                                                            <td>
                                                                ￥ 0.0765/小时
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                跨多个区域分布数据的单区域写入帐户
                                                            </td>
                                                            <td>
                                                                100 RU/s x 1.5 x N 个区域
                                                            </td>
                                                            <td>
                                                                ￥ 0.0765/小时
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                跨多个区域分布的多区域写入（以前称为多主数据库）帐户
                                                            </td>
                                                            <td>
                                                                100 RU/s x N 个区域
                                                            </td>
                                                            <td>
                                                                ￥ 0.102//小时
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        *可用性区域的自动缩放预配吞吐量无单独费用。
                                                    </div>
                                                    <br />
                                                    <div class="ms-date">
                                                        自动缩放以前称为“autopilot”。
                                                    </div>
                                                    <br />
                                                    <div class="ms-date">
                                                        必须为自动缩放预配的吞吐量至少预配 1,000 RU/s。有关详细信息，请参阅
                                                        <a href="https://docs.azure.cn/zh-cn/cosmos-db/"
                                                            style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                            文档
                                                        </a>
                                                        页面。
                                                    </div>
                                                    <br />
                                                </div>
                                            </div>
                                            <br />
                                            <h3>
                                                使用的存储
                                            </h3>
                                            <p>
                                                Azure Cosmos DB 提供不受限制的事务和分析存储，该存储按数据和索引在为异地复制选择的所有区域中使用的 SSD 支持的逻辑存储量（以
                                                GB 为单位）计费。例如，如果跨三个区域复制 Azure Cosmos DB 帐户，则将为这三个区域中的每个区域支付总存储成本。
                                            </p>
                                            <p>
                                                你的数据在两个不同的存储层（事务和分析）中进行管理，工作负载在相同的逻辑数据上运行，彼此之间互不干扰。虽然事务存储默认为始终启用，但必须在
                                                Azure Cosmos DB 容器上显式启用分析存储，以便使用
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/synapse-link"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    Azure Synapse Link
                                                </a>
                                                对 Azure Cosmos DB 中的数据运行分析。
                                            </p>
                                            <p>
                                                预配的吞吐量 (RU/s) 根据事务性存储量的大小按每 GB 存储量 10 RU/s 的速率缩放。要估算存储需求，请使用
                                                <a href="https://cosmos.azure.com/capacitycalculator/"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    容量规划器工具
                                                </a>
                                                ，并确保你已预配了足够的吞吐量来满足存储需求。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-2" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            使用的存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        事务存储（行导向）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 2.576/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        分析存储（列导向）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 0.149/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-2-n3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            使用的存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格 GB
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        事务存储（面向行）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 2.576/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <br />
                                            <h3>
                                                备份存储空间
                                            </h3>
                                            <p>
                                                帐户可以选择定期备份或连续备份。默认情况下，将在所有帐户上激活定期备份，并免费存储数据的两个备份副本。定期数据备份可以配置为地域、本地或区域可复原。有关详细信息，请参阅技术文档。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-6" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            定期备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            LRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            ZRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            RA-GRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        存储的定期备份数据（2 个副本）
                                                    </td>
                                                    <td>
                                                        每个副本的 GB 数
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        存储的定期备份数据（&gt;2 个副本)
                                                    </td>
                                                    <td>
                                                        每个副本的 GB 数
                                                    </td>
                                                    <td>
                                                        ￥0.786/月
                                                    </td>
                                                    <td>
                                                        ￥0.978/月
                                                    </td>
                                                    <td>
                                                        ￥1.566/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <p>
                                                可以使用 Core (SQL) API 或适用于 MongoDB 的 API
                                                在帐户上激活连续备份（预览版），而不使用定期备份。激活后，将根据所有选定的 Azure
                                                区域中存储的数据总量按月收取备份费用。从连续备份数据进行时间点还原，按还原到主要写入区域的数据的总 GB 数进行计费。
                                            </p>
                                            <!-- <table cellpadding="0" cellspacing="0" width="100%" id="cosmos-7">
                                         <tr>
                                             <th align="left"><strong>定期备份存储</strong></th>
                                             <th align="left"><strong>GB 总计</strong></th>
                                             <th align="left"><strong>每 GB 的价格</strong></th>
                                         </tr>
                                         <tr>
                                             <td>连续备份预览版数据</td>
                                             <td>Gb x 选定的 Azure 区域</td>
                                             <td>￥2.034/月</td>
                                         </tr>
                                         <tr>
                                             <td>时间点还原</td>
                                             <td>GB</td>
                                             <td>￥1.524/月</td>
                                         </tr>
                                     </table> -->
                                            <table cellpadding="0" cellspacing="0" id="cosmos-3-2" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            连续备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 7 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 30 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        ￥2.03/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        时间点还原
                                                    </td>
                                                    <td>
                                                        GB
                                                    </td>
                                                    <td>
                                                        ￥1.524/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-11-2" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            连续备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 7 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 30 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        ￥2.03/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        时间点还原
                                                    </td>
                                                    <td>
                                                        GBs
                                                    </td>
                                                    <td>
                                                        ￥1.02/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <div class="scroll-table" style="display: block;">
                                                <h3>
                                                    分析存储事务
                                                </h3>
                                                <p>
                                                    分析存储的 IO（输入/输出）事务按操作数计费。
                                                </p>
                                                <table cellpadding="0" cellspacing="0" id="cosmos-8" width="100%">
                                                    <tbody>
                                                        <tr>
                                                            <th align="left">
                                                                <strong>
                                                                    事务
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    操作
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    价格
                                                                </strong>
                                                            </th>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                写入操作 - 分析存储
                                                            </td>
                                                            <td>
                                                                10,000
                                                            </td>
                                                            <td>
                                                                ￥0.045
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                读取操作 - 分析存储
                                                            </td>
                                                            <td>
                                                                10,000
                                                            </td>
                                                            <td>
                                                                ￥0.015
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        如果需要更详细地估算工作负荷所需的分析存储操作，请使用
                                                        <a href="https://cosmos.azure.com/capacitycalculator/"
                                                            style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                            容量规划器工具
                                                        </a>
                                                        并参阅
                                                        <a href="https://docs.azure.cn/zh-cn/cosmos-db/analytical-store-introduction#analytical-store-pricing"
                                                            style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                            Azure
                                                            Cosmos DB 分析存储定价文档
                                                        </a>
                                                        ，了解更多详细信息。
                                                    </div>
                                                    <br />
                                                    <div class="ms-date">
                                                        注意：事务存储没有单独的 IO 费用，因为这是 RU 的一部分。
                                                    </div>
                                                </div>
                                            </div>
                                            <h3>带宽</h3>
                                            <p>Azure Cosmos DB 对从 Azure 云流出到 Internet 上的目的地或在区域之间传输 Azure WAN 的数据计费。</p>
                                            <h3>数据传入(流入量)</h3>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th align="left" style="width: 700px;">

                                                    </th>
                                                    <th align="left">
                                                        价格
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>到任何区域的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                                <tr>
                                                    <td>任何区域内的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                                <tr>
                                                    <td>可用性区域内或任何区域内的可用性区域之间的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                            </table>
                                            <h3>数据传出(流出量)</h3>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th  align="left"></th>
                                                    <th  align="left">前5GB/月</th>
                                                    <th  align="left">价格</th>
                                                </tr>
                                                <tr>
                                                    <td>到中国区域1 的 数据传输</td>
                                                    <td>免费</td>
                                                    <td>￥0.408/GB</td>
                                                </tr>
                                            </table>
                                            <br />
                                            <h3>
                                                多个区域
                                            </h3>
                                            <p>
                                                Azure Cosmos DB 通过多区域读取和
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/global-dist-under-the-hood"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    多区域写入
                                                </a>
                                                （以前称为“多主数据库”），跨所有 Azure 区域提供有保证的低延迟和高可用性。可随时向 Azure Cosmos DB 帐户
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/distribute-data-globally"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    添加区域或从中删除区域
                                                </a>
                                                。多个区域的计费影响在于，预配的吞吐量和使用的存储将乘以与你的帐户关联的区域数。
                                            </p>
                                            <!-- <h3>可用性区域</h3>
                                     <p>在 Azure 门户中选择要与 Azure Cosmos DB 帐户关联的区域时，可启用<a href="https://docs.microsoft.com/zh-cn/azure/availability-zones/az-region">可用性区域</a>。这样可以通过在给定地区中的多个区域复制数据，在该地区内提供额外的冗余。符合可用性区域条件的 Azure 区域为：UK South, Southeast Asia, East US, East US 2, Central US, West Europe, West US 2, Japan East, North Europe, France Central, and Australia East。</p>
                                     <p>计费影响如下：</p>
                                     <ul>
                                         <li>对于使用标准预配吞吐量的单区域写入帐户，每个可用性区域中的每秒请求单位数 (RU/s) 将乘以系数 1.25</li>
                                         <li>对于单区域无服务器帐户，请求单位数 (RU) 将乘以系数 1.25</li>
                                         <li>对于使用预配吞吐量的多区域写入（之前称为“多主数据库”）帐户，无计费影响</li>
                                     </ul> -->
                                        </div>
                                        <div class="tab-panel" id="tabContent1-3">
                                            <div class="scroll-table" style="display: block;">
                                                <h3>
                                                    无服务器
                                                </h3>
                                                <p>
                                                    采用
                                                    <b>
                                                        无服务器
                                                    </b>
                                                    方式，可以轻松运行流量较低的工作负荷。它可以按需处理间歇性突发，无需进行资源计划或管理，并且只对所使用的资源按每个数据库操作计费，无最低用量要求。作为无最低操作数和请求单位数
                                                    (RU)
                                                    的按请求计费模型，无服务器是运行不具有持续流量的小型应用程序的绝佳选择。
                                                </p>
                                                <table cellpadding="0" cellspacing="0" id="cosmos-9" width="100%">
                                                    <tbody>
                                                        <tr>
                                                            <th align="left">
                                                                <strong>
                                                                    无服务器
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    总请求单位数 (RU)
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    每 1M RU 的价格
                                                                </strong>
                                                            </th>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                无服务器请求单位数 (RU)
                                                            </td>
                                                            <td>
                                                                1,000,000
                                                            </td>
                                                            <td>
                                                                ￥2.36
                                                            </td>
                                                        </tr>
                                                        <!-- <tr>
                                                    <td>无服务器请求单位数 (RU)</td>
                                                    <td>1,000,000 x 1.25</td>
                                                    <td>￥2.36</td>
                                                </tr> -->
                                                    </tbody>
                                                </table>
                                                <br />
                                            </div>
                                            <h3>
                                                使用的存储
                                            </h3>
                                            <p>
                                                Azure Cosmos DB 提供不受限制的事务和分析存储，该存储按数据和索引在为异地复制选择的所有区域中使用的 SSD 支持的逻辑存储量（以
                                                GB 为单位）计费。例如，如果跨三个区域复制 Azure Cosmos DB 帐户，则将为这三个区域中的每个区域支付总存储成本。
                                            </p>
                                            <p>
                                                你的数据在两个不同的存储层（事务和分析）中进行管理，工作负载在相同的逻辑数据上运行，彼此之间互不干扰。虽然事务存储默认为始终启用，但必须在
                                                Azure Cosmos DB 容器上显式启用分析存储，以便使用
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/synapse-link"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    Azure Synapse Link
                                                </a>
                                                对 Azure Cosmos DB 中的数据运行分析。
                                            </p>
                                            <p>
                                                预配的吞吐量 (RU/s) 根据事务性存储量的大小按每 GB 存储量 10 RU/s 的速率缩放。要估算存储需求，请使用
                                                <a href="https://cosmos.azure.com/capacitycalculator/"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    容量规划器工具
                                                </a>
                                                ，并确保你已预配了足够的吞吐量来满足存储需求。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-1" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            使用的存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        事务存储（行导向）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 2.576/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        分析存储（列导向）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 0.149/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-db-1-n3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            使用的存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        事务存储（面向行）
                                                    </td>
                                                    <td>
                                                        1 GB
                                                    </td>
                                                    <td>
                                                        ￥ 2.576/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <br />
                                            <h3>
                                                备份存储空间
                                            </h3>
                                            <p>
                                                帐户可以选择定期备份或连续备份。默认情况下，将在所有帐户上激活定期备份，并免费存储数据的两个备份副本。定期数据备份可以配置为地域、本地或区域可复原。有关详细信息，请参阅技术文档。
                                            </p>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-10" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            定期备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            LRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            ZRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            RA-GRS 每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        存储的定期备份数据（2 个副本）
                                                    </td>
                                                    <td>
                                                        每个副本的 GB 数
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        存储的定期备份数据（&gt;2 个副本)
                                                    </td>
                                                    <td>
                                                        每个副本的 GB 数
                                                    </td>
                                                    <td>
                                                        ￥0.786/月
                                                    </td>
                                                    <td>
                                                        ￥0.978/月
                                                    </td>
                                                    <td>
                                                        ￥1.566/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <p>
                                                可以使用 Core (SQL) API 或适用于 MongoDB 的 API
                                                在帐户上激活连续备份（预览版），而不使用定期备份。激活后，将根据所有选定的 Azure
                                                区域中存储的数据总量按月收取备份费用。从连续备份数据进行时间点还原，按还原到主要写入区域的数据的总 GB 数进行计费。
                                            </p>
                                            <!-- <table cellpadding="0" cellspacing="0" width="100%" id="cosmos-11">
                                         <tr>
                                             <th align="left"><strong>定期备份存储</strong></th>
                                             <th align="left"><strong>GB 总计</strong></th>
                                             <th align="left"><strong>每 GB 的价格</strong></th>
                                         </tr>
                                         <tr>
                                             <td>连续备份预览版数据</td>
                                             <td>Gb x 选定的 Azure 区域</td>
                                             <td>￥2.034/月</td>
                                         </tr>
                                         <tr>
                                             <td>时间点还原</td>
                                             <td>GB</td>
                                             <td>￥1.524/月</td>
                                         </tr>
                                     </table> -->
                                            <!-- <table cellpadding="0" cellspacing="0" width="100%" id="cosmos-11-3">
                                        <tr>
                                            <th align="left"><strong>定期备份存储</strong></th>
                                            <th align="left"><strong>GB 总计</strong></th>
                                            <th align="left"><strong>价格 per GB</strong></th>
                                        </tr>
                                      
                                        <tr>
                                            <td>时间点还原</td>
                                            <td>GBs</td>
                                            <td>￥1.02/月</td>
                                        </tr>
                                    </table> -->
                                            <table cellpadding="0" cellspacing="0" id="cosmos-3-3" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            连续备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 7 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 30 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        ￥2.03/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        时间点还原
                                                    </td>
                                                    <td>
                                                        GB
                                                    </td>
                                                    <td>
                                                        ￥1.524/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <table cellpadding="0" cellspacing="0" id="cosmos-11-4" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            连续备份存储
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            GB 总计
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            每 GB 的价格
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 7 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        免费/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        连续备份数据 – 30 天保留期
                                                    </td>
                                                    <td>
                                                        GB × N 个区域
                                                    </td>
                                                    <td>
                                                        ￥2.03/月
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        时间点还原
                                                    </td>
                                                    <td>
                                                        GB
                                                    </td>
                                                    <td>
                                                        ￥1.02/月
                                                    </td>
                                                </tr>
                                            </table>
                                            <div class="scroll-table" style="display: block;">
                                                <h3>
                                                    分析存储事务
                                                </h3>
                                                <p>
                                                    分析存储的 IO（输入/输出）事务按操作数计费。
                                                </p>
                                                <table cellpadding="0" cellspacing="0" id="cosmos-12" width="100%">
                                                    <tbody>
                                                        <tr>
                                                            <th align="left">
                                                                <strong>
                                                                    事务
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    操作
                                                                </strong>
                                                            </th>
                                                            <th align="left">
                                                                <strong>
                                                                    价格
                                                                </strong>
                                                            </th>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                写入操作 - 分析存储
                                                            </td>
                                                            <td>
                                                                10,000
                                                            </td>
                                                            <td>
                                                                ￥0.045
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                读取操作 - 分析存储
                                                            </td>
                                                            <td>
                                                                10,000
                                                            </td>
                                                            <td>
                                                                ￥0.015
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        如果需要更详细地估算工作负荷所需的分析存储操作，请使用
                                                        <a href="https://cosmos.azure.com/capacitycalculator/"
                                                            style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                            容量规划器工具
                                                        </a>
                                                        并参阅
                                                        <a href="https://docs.azure.cn/zh-cn/cosmos-db/analytical-store-introduction#analytical-store-pricing"
                                                            style="color: #006fc3; font-size: 12px; display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                            Azure
                                                            Cosmos DB 分析存储定价文档
                                                        </a>
                                                        ，了解更多详细信息。
                                                    </div>
                                                    <br />
                                                    <div class="ms-date">
                                                        注意：事务存储没有单独的 IO 费用，因为这是 RU 的一部分。
                                                    </div>
                                                </div>
                                                <br />
                                            </div>
                                            <h3>带宽</h3>
                                            <p>Azure Cosmos DB 对从 Azure 云流出到 Internet 上的目的地或在区域之间传输 Azure WAN 的数据计费。</p>
                                            <h3>数据传入(流入量)</h3>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th style="width:700px">

                                                    </th>
                                                    <th align="left">
                                                        价格
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>到任何区域的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                                <tr>
                                                    <td>任何区域内的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                                <tr>
                                                    <td>可用性区域内或任何区域内的可用性区域之间的数据传输</td>
                                                    <td>免费</td>
                                                </tr>
                                            </table>
                                            <h3>数据传出(流出量)</h3>
                                            <table cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th  align=""></th>
                                                    <th  align="left">前5GB/月</th>
                                                    <th  align="left">价格</th>
                                                </tr>
                                                <tr>
                                                    <td>到中国区域1 的 数据传输</td>
                                                    <td>免费</td>
                                                    <td>￥0.408/GB</td>
                                                </tr>
                                            </table>
                                            <h3>
                                                多个区域
                                            </h3>
                                            <p>
                                                Azure Cosmos DB 通过多区域读取和
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/global-dist-under-the-hood"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    多区域写入
                                                </a>
                                                （以前称为“多主数据库”），跨所有 Azure 区域提供有保证的低延迟和高可用性。可随时向 Azure Cosmos DB 帐户
                                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/distribute-data-globally"
                                                    style="display: inline-block; float: none; margin: 0;padding: 0;background: transparent;">
                                                    添加区域或从中删除区域
                                                </a>
                                                。多个区域的计费影响在于，预配的吞吐量和使用的存储将乘以与你的帐户关联的区域数。
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <div class="more-detail">
                                <h2>
                                    常见问题
                                </h2>
                                <em>
                                    全部展开
                                </em>
                                <ul>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question1">
                                                请求单位是什么？
                                            </a>
                                            <section>
                                                <p>
                                                    请求单位 (RU) 是 Azure Cosmos DB 中吞吐量的衡量单位。1 个 RU 对应于获取 1KB 文档的吞吐量。在
                                                    DocumentDB 中进行的每个操作（包括读、写、SQL
                                                    查询和执行存储的程序）都将具有一个确定的请求单位值，该值基于完成该操作所需的吞吐量。你无需考虑 CPU、IO
                                                    和内存，以及它们会怎样影响你的应用程序吞吐量，而是可以根据一个请求单位度量值进行考虑。
                                                </p>
                                                <p>
                                                    通过预配 RU 的每秒或一分钟的存储桶所使用的请求单位是相同的。
                                                </p>
                                                <p>
                                                    有关请求单位的详细信息和确定集合需求的帮助，请查看
                                                    <a href="https://docs.azure.cn/zh-cn/documentdb/documentdb-request-units/"
                                                        id="pricing-cosmos-db_docs-documentdb-request-units"
                                                        style="color: #00a8d9;">
                                                        Azure Cosmos DB 中的请求单位
                                                    </a>
                                                    。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question2">
                                                请求单位使用情况如何显示在我的帐单上？
                                            </a>
                                            <section>
                                                <p>
                                                    在此期间，根据你的 Azure Cosmos DB 帐户下预配的总体容量 (RU/sec)，采用波动平稳的可预测小时费率进行计费。
                                                </p>
                                                <p>
                                                    如果分别使用 500 RU/秒和 700 RU/秒两个分区创建帐户，则总预配容量将达到 1,200 RU/秒。因此，计费金额为 12 x
                                                    ¥ 0.051 = ¥ 0.612 /小时。
                                                </p>
                                                <p>
                                                    如果需要更改吞吐量，每个分区的容量增加了 500 RU/秒，同时还使用 20,000 RU/秒创建了新的无限存储容器，则预配的总体容量为
                                                    22,200 RU/秒（1,000 RU/秒 + 1,200 RU/秒 + 20,000RU/秒）。这时，帐单将变为：¥ 0.051 x
                                                    222 = ¥ 11.322/小时。
                                                </p>
                                                <p>
                                                    在一个月的 720 小时中，如果有 500 小时预配为 1,200 RU/秒，有 220 小时预配为 22,200
                                                    RU/秒，则每月帐单将显示：500 x ¥ 0.612/小时 + 220 x ¥ 11.322/小时 = ¥ 2,796.84。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question9">
                                                每分钟请求单位的工作原理
                                            </a>
                                            <section>
                                                <p>
                                                    除了常规的预览吞吐量，现在可以预配附加的每分钟请求单位。你可以在 UTC 分钟窗口中使用这些附加吞吐量单位。对于容器中预配的每个 100
                                                    RU/秒，如果启用每分钟请求单位，则每分钟能够额外使用 1,000 个请求单位。
                                                </p>
                                                <p>
                                                    例如，如果预配了 400 个 RU/秒，则可以使用附加的 4,000 个每秒请求单位。假如在中午 12 点整时，应用程序需要超过 400
                                                    个 RU/秒。从下午 12:00:01 到12:01:00，应用程序将能够使用 4,000 个额外的请求单位，同时可以继续使用预配的
                                                    400 RU/秒 吞吐量。从下午 12:00:01 开始，如果在下午 12:01:00 之前使用了全部的 4,000
                                                    个请求单元，直到下一个 UTC 分钟（从下午 12:01:01 开始），才能使用其他请求单位。如果在给定的分钟时段中不使用全部的
                                                    4,000 个请求单元，剩余的请求单位不会累计到下一个分钟时段。
                                                </p>
                                                <p>
                                                    有关详细信息，请查看
                                                    <a href="https://docs.azure.cn/cosmos-db/request-units-per-minute/"
                                                        id="pricing-cosmos-db_docs-request-units-per-minute"
                                                        style="color: #00a8d9;">
                                                        Azure Cosmos DB 中的每分钟请求单位数
                                                    </a>
                                                    。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question3">
                                                如果为容器指定自己的性能，会如何对存储计费？
                                            </a>
                                            <section>
                                                <p>
                                                    存储容量按一个月内每小时的最大数据存储量（以 GB 为单位）计费。例如，如果你在前半个月使用了 100 GB
                                                    的存储空间，而在后半个月使用了 50 GB 的存储空间，则该月将按 75 GB 的等效存储空间进行计费。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question4">
                                                如果容器存在时间不足一个小时如何计费？
                                            </a>
                                            <section>
                                                <p>
                                                    将按容器存在的每小时的统一费率对你收费，无论使用量是多少，也无论集合存在时间是否不足一个小时。例如，如果你创建一个容器，然后在 5
                                                    分钟后删除它，那么你的帐单将反映 1 个单位小时的收费。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <!-- <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="Storage_question5">在我升级容器后，收费率何时更改？</a>
                                <section>
                                    <p>如果你为容器定义自己的性能，并在上午 9:30 从 400 个 RU 升级到 1,000 个 RU，然后在上午 10:45 降级回 400 个 RU，那么将按两小时的 1,000 个 RU 对你收费。</p>
									<p>如果你选择预定义的集合性能级别，并在上午 9:30 从 S1 集合升级到 S3 集合，然后在上午 10:45 降级回 S1，那么将按两小时的 S3 对你收费。</p>
                                </section>

                            </div>
                        </li> -->
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question6">
                                                如何增加或减少每个集合的吞吐量？
                                            </a>
                                            <section>
                                                <p>
                                                    你可以使用
                                                    <a href="https://portal.azure.cn/" id="cosmos-db_azure-menhu"
                                                        style="color: #00a8d9;">
                                                        Azure 门户
                                                    </a>
                                                    中一个受支持的 SDK 或 REST API 来增加或减少 Azure Cosmos DB 帐户内每个容器的请求单位数。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <!--
						<li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="Storage_question7">我是否可以用一个数据库帐户购买具有预定义性能和用户定义的性能的多个集合？</a>
                                <section>
                                    <p>是的，你可以拥有多个集合。但是，对于新应用程序，建议使用用户定义的性能来创建集合，因为它们可支持较大的存储大小和预配的吞吐量，以及灵活而精细的计费模型。</p>
                                </section>
                            </div>
                        </li>
						-->
                                    <!-- <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="Storage_question8">如何从 S1/S2/S3 集合移动到单个分区？</a>
                                <section>
                                     <p>若要将 S1、S2 或 S3 性能级别的集合移动到具有相同存储大小的单个分区，请查看<a id="pricing-cosmos-db_docs-documentdb-performance-levels-2" style="color: #00a8d9;" href="//docs.azure.cn/documentdb/documentdb-performance-levels/">使用 Azure 门户更改性能级别</a>。</p>
                                     <p>若要将现有的单个集合移动到无限存储容器中，请查看<a id="pricing-cosmos-db_docs-documentdb-partition-data-2" style="color: #00a8d9;" href="//docs.azure.cn/zh-cn/documentdb/documentdb-partition-data/">Azure Cosmos DB 中的分区和缩放</a>。</p>
                                </section>
                            </div>
                        </li> -->
                                    <!-- <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="Storage_question10">利用 10 GB 容器取代 S1/S2/S3 集合时，可以享有哪些优势？</a>
                                <section>
                                     <p>在某个入口点，单个分区的吞吐量将超过 S1 (400 RU/秒对比 250 RU/秒)，并且价格更低。也可以增加到 10,000 RU/秒，对比 S3 的 2,500 RU/秒。新预配模型的好处是可以按 100 RU/秒的增量增加，因此当你仅需要 1,200 RU/秒 时，不必付款购买 S3 的 2,500 RU/秒。</p>
                                </section>
                            </div>
                        </li> -->
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question11">
                                                Azure Cosmos DB如何才能停止计费？
                                            </a>
                                            <section>
                                                <p>
                                                    只有删除才能停止计费。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question11">
                                                Azure Cosmos DB中的“自动驾驶”和“自动缩放”之间有什么区别？
                                            </a>
                                            <section>
                                                <p>
                                                    “自动缩放”或“自动缩放提供的吞吐量”是该功能的更新名称，以前称为“自动驾驶仪”。
                                                    在当前版本的自动缩放中，我们添加了新功能，包括设置自定义最大 RU/秒 和程序支持的功能。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="Storage_question11">
                                                自动缩放如何显示在我的账单上？
                                            </a>
                                            <section>
                                                <p>
                                                    在单主帐户中，每 100 RU/秒 的自动扩展速率是标准（手动）预配置吞吐量的 1.5 倍。
                                                    在您的账单上，您将看到现有的标准配置吞吐量表。 该仪表的数量将乘以 1.5。 例如，如果系统在一小时内扩展到的最高 RU/秒 为
                                                    6000 RU/秒，则该小时将向您收取 60 x 1.5 = 90 计量表的费用。
                                                    <br />
                                                    <br />
                                                    在多主机帐户中，每 100 RU/秒 的自动扩展速率与标准（手动）配置的多主机吞吐量的速率相同。 在您的账单上，您将看到现有的多主仪表。
                                                    由于速率相同，因此，如果使用自动缩放，将看到与标准吞吐量相同的数量。
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>Azure Cosmos DB 服务在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table>
            </div> -->
                        <div class="pricing-page-section">
                            <h2>
                                支持和服务级别协议
                            </h2>
                            <p>
                                如有任何疑问或需要帮助，请访问
                                <a href="https://support.azure.cn/zh-cn/support/contact" id="cosmos-db-contact-page">
                                    Azure 支持
                                </a>
                                选择自助服务或者其他任何方式联系我们获得支持。
                            </p>
                            <p>
                                在中国由世纪互联运营的
                                <a href="../../../home/<USER>/cosmos-db/index.html">
                                    Azure Cosmos DB
                                </a>
                                是
                                <a href="https://docs.azure.cn/zh-cn/cosmos-db/distribute-data-globally/">
                                    分布式
                                </a>
                                多模型数据库服务。它在中国不同 Azure 数据中心提供统包数据分发，无论您的用户位于何处，其均可以透明方式调整及复制您的数据。该服务提供全面的 99.99%
                                服务级别协议，包括对配置有五种一致性水平中任意一种的单个 Azure 区域的 Cosmos DB 数据库帐户，或是配置有四种松散一致性水平中任意一种的跨多个 Azure
                                区域的数据库帐户的吞吐量、一致性、可用性和延迟保证。此外，除一致性水平选择之外，Cosmos DB 还提供跨两个或多个 Azure 区域数据库帐户的读取可用性为 99.999%
                                的服务级别协议。
                            </p>
                            <p>
                                若要了解有关我们的服务级别协议的详细信息，请访问
                                <a href="../../../support/sla/cosmos-db/index.html" id="cosmos-db-pricing_sla"
                                    target="_blank">
                                    服务级别协议
                                </a>
                                页。
                            </p>
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
        -->
                        <!--END: Support and service code chunk-->
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="OjsWoQVXpPbBWmx_EfEvSB9J4uZmwLRkYfngL_hFqYhk7MhT795OsYppWeY7xGSts5Es-FW3ZADwdz1-oExL44AKvi4a2DNSQybdN2CL6qQ1" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
    </script>
    <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>