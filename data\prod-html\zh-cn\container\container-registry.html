<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure 容器注册表, Contariner Registry, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="Azure 容器注册表是用于为所有 Azure 容器部署的公用存储管理专用 Docker 注册表的服务。立即开始免费试用。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   容器注册表价格_容器注册表价格估算 -  Azure云服务
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/container-registry/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="12/10/2018" ms.service="container-registry" wacn.date="12/10/2018">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/container-registry_banner.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/container-registry.svg"/>
          <h2>
           容器注册表
           <span>
            Container Registry
           </span>
          </h2>
          <h4>
           将 Docker 专用注册表作为一流的 Azure 资源进行管理
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <div class="pricing-page-section">
          <p>
           Azure 容器注册表可存储专用 Docker 容器映像，可在 Azure 上实现容器工作负荷的可缩放检索的快速近网部署。其他功能包括异地复制、Docker Content Trust 映像签名、Helm 图表存储库和任务基础计算，可用于生成、测试和修补容器工作负载。
          </p>
         </div>
         <h3>
          定价详细信息
         </h3>
         <div class="tags-date" data-filtered="filtered">
          <div class="ms-date" data-filtered="filtered">
           *以下价格均为含税价格。
          </div>
          <br data-filtered="filtered"/>
          <div class="ms-date" data-filtered="filtered">
           *每月价格估算基于每个月 744 小时的使用量。
          </div>
         </div>
         <div class="scroll-table">
          <table cellpadding="0" cellspacing="0">
           <thead>
            <tr>
             <th align="left" width="25%">
             </th>
             <th align="left" width="25%">
              <strong>
               基本
              </strong>
             </th>
             <th align="left" width="25%">
              <strong>
               标准
              </strong>
             </th>
             <th align="left">
              <strong>
               高级
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
              每日价格
             </td>
             <td class="left_align">
              1.6953
             </td>
             <td class="left_align">
              6.784
             </td>
             <td class="left_align">
              16.96
             </td>
            </tr>
            <tr>
             <td class="left_align">
              包含的存储 (GB)
             </td>
             <td class="left_align">
              10 GB
             </td>
             <td class="left_align">
              100 GB
             </td>
             <td class="left_align">
              500高级跨多个并发节点提供 docker 拉取请求的增强型吞吐量
             </td>
            </tr>
            <tr>
             <td class="left_align">
              总 Webhook
             </td>
             <td class="left_align">
              2
             </td>
             <td class="left_align">
              10
             </td>
             <td class="left_align">
              500（可根据请求提供其他信息）
             </td>
            </tr>
            <tr>
             <td class="left_align">
              异地复制
             </td>
             <td class="left_align">
              不支持
             </td>
             <td class="left_align">
              不支持
             </td>
             <td class="left_align">
              受支持￥16.96/复制的区域
             </td>
            </tr>
           </tbody>
          </table>
         </div>
         <p>
          标准网络费用适用。
         </p>
         <h3>
          附加存储
         </h3>
         <p>
          附加的存储将以所有服务层的每日费率提供。尽管存储的容量可以超过每层所包含的存储限制，但你需要按每日费率为超出指定限制的映像存储付费。
         </p>
         <table cellpadding="0" cellspacing="0">
          <thead>
           <tr>
            <th align="left">
             层
            </th>
            <th align="left">
             每 GB 价格
            </th>
           </tr>
          </thead>
          <tbody>
           <tr>
            <td>
             基本、标准、高级
            </td>
            <td>
             ￥1.0176/月
            </td>
           </tr>
          </tbody>
         </table>
         <h3>
          容器内部版本
         </h3>
         <table cellpadding="0" cellspacing="0">
          <thead>
           <tr>
            <th align="left">
             层
            </th>
            <th align="left">
             每 CPU 价格
            </th>
           </tr>
          </thead>
          <tbody>
           <tr>
            <td>
             基本、标准、高级
            </td>
            <td>
             ￥0.000508/秒
            </td>
           </tr>
          </tbody>
         </table>
         <!-- END: Table2-Content-->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em id="ws_unfolded_all">
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="container-registry-que-q1">
             能否超出每层的使用量限制？如果能，超额的使用量如何收费？如果不能，达到限制时服务会发生什么情况？
            </a>
            <section>
             <p>
              基本、标准和高级层包含其定价范围内的的存储量。随着存储超过所含量，将针对额外的存储对注册表收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="container-registry-que-q2">
             能否升级或降级服务层？如果能，这对我的计费将有何影响？
            </a>
            <section>
             <p>
              是的，可以使用 Azure CLI 更改托管层中的 SKU。更改前，用户根据先前的 SKU 价格付费；更改后，将根据新的 SKU 价格付费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="container-registry-que-q3">
             我的帐单上以什么形式反映使用量？
            </a>
            <section>
             <p>
              帐单将显示基于注册表 SKU 的每个托管注册表的费用，以及所有网络费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="container-registry-que-q4">
             是否会产生数据传输费用？
            </a>
            <section>
             <p>
              标准网络费用适用于来自 Azure 数据中心或该数据中心之间的网络出口。建议在与容器部署相同的数据中心维护注册表。查看异地复制预览功能，将多个区域视为一个区域进行管理，提供跨多个区域的近网部署。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>容器注册表在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国北部数据中心</td>
                    </tr>
                </table>
            </div> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="app-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/container-registry/index.html" id="pricing_container-registry_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="FtJS7EhZBT6rgbp0PkQRqZsvT4EkKorxier871BisK9SjwrzF-3NCsrqEl_VYuhENbkKwc9cRYh6MY_Z2AzDIMXeWXd32LE1Vl7rUDgXkV41" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
