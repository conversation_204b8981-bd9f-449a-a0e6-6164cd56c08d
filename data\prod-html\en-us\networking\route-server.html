<!DOCTYPE html>
<html lang="en-US">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <meta name="keywords" content="Azure, Backup data,pricing"/>
    <meta name="description"
          content="Learn more about the pricing details for Azure Backup. Azure Backup is a reliable, low-cost, and scalable solution, allowing for backup of local data into the cloud. Without any capital investment."/>
    <title>Azure Route Server Pricing Details - Azure Cloud Computing</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/Static/Favicon/apple-touch-icon.png">
    <link rel="shortcut icon" href="/Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="icon" href="/Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="manifest" href="/Static/Favicon/manifest.json">
    <link rel="mask-icon" href="/Static/Favicon/safari-pinned-tab.svg" color="#0078D4">
    <meta name="msapplication-config" content="/Static/Favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
    <link rel="canonical" href="https://azure.microsoft.com/pricing/details/backup/"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="/Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->

    <!-- END: Page Style -->

    <!-- BEGIN: Minified Page Style -->
    <link href='/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css' rel='stylesheet'/>
    <!-- END: Minified Page Style -->

    <link rel="stylesheet" href="/StaticService/css/service.min.css"/>
</head>

<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>

<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }

    .updatetime {
        color: black;
        text-align: right;
        font-size: 12px;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage"></div>
</div>

<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li><span></span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">加载中...</option>
                        </select>
                        <span class="icon icon-arrow-top"></span>
                    </div>
                    <tags ms.service="en-us-route-server" ms.date="12/10/2018" wacn.date="12/10/2018"></tags>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">&nbsp;</div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">Not available in the selected region</div>
                        </div>
                    </div>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/container-registry_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/resources/self-serve/private-link.svg"/>
                                <h2>Azure Route Server</h2>
                                <h4>Simplify operations management of your network appliances</h4>
                                <div class = "updatetime">
                                    Updated on：2025/08/05
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->

                    <div class="pricing-page-section"></div>

                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>OS/Software:</label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                            <span class="selected-item">Backup</span>
                                            <i class="icon"></i>
                                            <ol class="tab-items">
                                                <li class="active"><a href="javascript:void(0)"
                                                                      data-href="#tabContent1" id="home_backup">Backup</a></li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                            <option selected="selected" data-href="#tabContent1" value="Backup">
                                                Backup
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container" style="display: none;">
                                        <label>Region：</label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                            <span class="selected-item">China East 3</span>
                                            <i class="icon"></i>
                                            <ol class="tab-items">
                                                <!-- <li class="active"><a href="javascript:void(0)" data-href="#east-china3" id="east-china3">China East 3</a></li> -->
                                                <li><a href="javascript:void(0)" data-href="#north-china3"
                                                       id="north-china3">China North 3</a></li>
                                                <li><a href="javascript:void(0)" data-href="#east-china2"
                                                       id="east-china2">China East 2</a>
                                                </li>
                                                <li><a href="javascript:void(0)" data-href="#north-china2"
                                                       id="north-china2">China North 2</a></li>
                                                <li><a href="javascript:void(0)" data-href="#east-china"
                                                       id="east-china">China East</a></li>
                                                <li><a href="javascript:void(0)" data-href="#north-china"
                                                       id="north-china">China North</a></li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                            <!-- <option selected="selected" data-href="#east-china3"
                                                value="east-china3">China East 3
                                        </option> -->
                                            <option data-href="#north-china3" value="north-china3">China North 3
                                            </option>
                                            <option selected="selected" data-href="#east-china2"
                                                    value="east-china2">China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">China East</option>
                                            <option data-href="#north-china" value="north-china">China North
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content" id="tabContent1">
                            <div class="tab-panel" id="tabContent1">
                            <!-- BEGIN: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 content 3 -->
                                <div class="tab-content">

                                    <div class="scroll-table" style="display: block;">
                                        <p>Azure Route Server enables network appliances to exchange route information with Azure virtual networks dynamically. Configure
                                            your network appliances and Azure ExpressRoute and VPN gateways to automatically take the latest route information from Azure
                                            Route Server instead of manually talking to each network.</p>
                                        <table cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <th align="left"></th>
                                                <th align="left"><strong>Price</strong></th>
                                            </tr>
                                            <tr>
                                                <td>Azure Route Server</td>
                                                <td>￥4.58 /hour</td>
                                            </tr>
                                            <tr>
                                                <td>Routing Infrastructure Unit</td>
                                                <td>￥1.0176 /hour</td>
                                            </tr>
                                        </table>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            <!-- END: TAB-CONTAINER-3 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->

                    <div class="pricing-page-section">
                        <h2>Support &amp; SLA</h2>
                        <p>If you have any questions or need help, please visit <a href="https://support.azure.cn/en-us/support/contact" id="networking-contact-page">
                            Azure Support</a> and select self-help service or any other method to contact us for support.</p>

                        <p>The virtual network is free of charge and doesn’t provide service level agreements. To learn more about the details of
                            our service level agreement, please visit the <a href="/en-us/support/sla/route-server/index.html" id="pricing_route-server_sla">Service Level
                                Agreements</a> page.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->

<!-- BEGIN: Footer -->
<div class="public_footerpage"></div>
<!--END: Common sidebar-->


<link href="/Static/CSS/Localization/zh-cn.css" rel="stylesheet">
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="ufpRkXvp7dB8fmcGPMCtuCtpzKfPRC8l-TLfAkhjGPCDN-e1hR3BPGETr6N7js4U6oKsVshGMlLgqq-nieTvhhvZzZkOco-yvEurbmpICkk1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>

<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>

<!-- BEGIN: Minified RequireJs -->
<script src='/Static/Scripts/global.config.js' type='text/javascript'></script>
<script src='/Static/Scripts/require.js' type='text/javascript'></script>
<script src=' /Static/Scripts/pricing-page-detail.js' type='text/javascript'></script>
<script src='/Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js' type='text/javascript'></script>
<!-- END: Minified RequireJs -->

<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js"></script>

<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->

<script type="text/javascript" src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../Static/Scripts/wacndatatracker.js"></script>


<script type="text/javascript" src="/common/useCommon.js"></script>
</body>

</html>