<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Azure Service Fabric, pricing, charges" name="keywords"/>
    <meta content="About Azure Service Fabric pricing details. Service Fabric can be applied to a Windows Server virtual machine of any standard size. You will be charged only for the calculation instances selected by the user and for the storage and network resources used to build Service Fabric clusters. Service Fabric services are free of charge. A 1RMB Trial gets you ￥1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer."
          name="description"/>
    <title>
        Service Fabric Pricing Details - Azure Could Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/service-fabric/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-service-fabric" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/media,images,production,service-fabric_background.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/documents/<EMAIL>"/>
                                <h2>
                                    Service Fabric
                                </h2>
                                <h4>
                                    Develop microservices and orchestrate containers on Windows or Linux
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Service Fabric simplifies development of microservice architecture applications, enabling developers to focus on
                            building features that raise end-user value without restrictions, rather than the management infrastructure.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <h3>
                                Service Fabric on Azure
                            </h3>
                            <p>
                                Service Fabric can be applied to a Windows Server virtual machines or Linux virtual machines of any standard size
                                (only the preview version of Service Fabric is currently available on Linux). Service Fabric can also be used under
                                the preview version, meanwhile Ubuntu images can run on Azure. You will be charged only for the compute instances
                                you select and for the storage and network resources used to build Service Fabric clusters. The Service Fabric
                                services are free of charge. For details on the pricing of necessary resources, please visit the following webpages:
                            </p>
                            <p>
                                <a href="/en-us/pricing/details/virtual-machines/" id="sla-Global-distribution01" target="_blank">
                                    Virtual Machines
                                    Pricing
                                </a>
                            </p>
                            <p>
                                <a href="/en-us/pricing/details/storage/" id="sla-Global-distribution02" target="_blank">
                                    Storage Pricing
                                </a>
                            </p>
                            <p>
                                <a href="/en-us/pricing/details/ip-addresses/" id="sla-Global-distribution03" target="_blank">
                                    IP Address Pricing
                                </a>
                                (if
                                your Service Fabric cluster requires other load balancers).
                            </p>
                            <!-- <p>To better estimate the cost of required Service Fabric clusters, please use the <a id="sla-Global-distribution04"
                                               style="color: #00a8d9;" href="/en-us/pricing/calculator/" target="_blank">Azure Pricing Calculator</a>.</p> -->
                            <!-- END: Table1-Content-->
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->
                        <!-- BEGIN: TAB-CONTAINER-2 -->
                        <div class="tab-control-container tab-active" id="tabContent1">
                            <!-- BEGIN: Table1-Content-->
                            <h3>
                                Service Fabric for Windows Server
                            </h3>
                            <p>
                                Service Fabric allows free downloading for Windows Server, enabling you to create Service Fabric clusters locally or
                                in other clouds.
                            </p>
                            <!-- END: Table1-Content-->
                        </div>
                        <!-- END: TAB-CONTAINER-2 -->
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <!-- <div class="pricing-page-section">
                                     <h2>Region</h2>
                                     <p>Service Fabric services are available in the following regions:</p>
                                     <table cellpadding="0" cellspacing="0" class="table-col6">
                                         <tr>
                                             <th align="left"><strong>Territory</strong></th>
                                             <th align="left"><strong>Region</strong></th>
                                         </tr>
                                         <tr>
                                             <td>Mainland China</td>
                                             <td>China North, China East</td>
                                         </tr>
                                     </table> -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="fabric-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            Service Fabric is a free service, so it’s not applicable to the Service Level Agreement. The availability of Service
                            Fabric clusters is based on the Service Level Agreement of the virtual machine and storage resources used. To learn more
                            about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/service-fabric/" id="pricing_service-fabric_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--


                                         <h2>Support &amp; SLA</h2>
                                         <p>Azure Support Features:</p>
                                         <p>We provide users with the following free support services:</p>
                                         <table cellpadding="0" cellspacing="0" class="table-col6">
                                             <tr>
                                                 <th align="left"> </th>
                                                 <th align="left"><strong>Supported or not</strong></th>
                                             </tr>
                                             <tr>
                                                 <td>Billing and Subscription Management</td>
                                                 <td><i class="icon icon-tick"></i></td>
                                             </tr>
                                             <tr>
                                                 <td>Service Dashboard</td>
                                                 <td><i class="icon icon-tick"></i></td>
                                             </tr>
                                             <tr>
                                                 <td>Web Event Submission</td>
                                                 <td><i class="icon icon-tick"></i></td>
                                             </tr>
                                             <tr>
                                                 <td>Unlimited Disruption/Restoration</td>
                                                 <td><i class="icon icon-tick"></i></td>
                                             </tr>
                                             <tr>
                                                 <td>Telephone Support</td>
                                                 <td><i class="icon icon-tick"></i></td>
                                             </tr>
                                             <tr>
                                                 <td>ICP Filing Support</td>
                                                 <td><i class="icon icon-tick"></i></td>
                                             </tr>
                                         </table>
                                         <p>You can <a href="/en-us/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">request support online</a> or contact us via the service hotline.</p>
                                         <h2>Service hotline:</h2>
                                         <ul>
                                             <li>************</li>
                                             <li>010-84563652</li>
                                         </ul>
                                         <p>Community help: <a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">Visit MSDN</a></p>
                                         <p>For more support information, please visit <a href="/en-us/support/plans/" id="stor-sla-info">Azure Support Plans</a></p>


                                 -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="cO-ztjsaj4AipEd8qChMMWP4d1I0Zf9H9N-JVkDy1ZYltjKc0raP1R4b6ffJjWfcuFPkijs_T5tHY8B40px7VeRBQuNX3FsUo29Oy9k63dM1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
