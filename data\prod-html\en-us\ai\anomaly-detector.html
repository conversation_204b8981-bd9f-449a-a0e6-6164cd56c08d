<!DOCTYPE html>
<html lang="en-us">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="认知服务价格,服务报价,价格估算" name="keywords"/>
    <meta content="了解 认知服务（Cognitive-Services） 价格详情。检测图片中人脸位置，依照视觉相似度做人脸比对、分组、以及辨识由用户标记过身份的人脸。" name="description"/>
    <title>
        Azure AI Anomaly Detector Pricing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/cognitive-services/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css?2019/11/18 10:24:40" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css?2019/11/18 10:24:40" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css?2019/11/18 10:24:40" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "2019/11/18 10:24:40";
    window.currentLocale = "en-Us";
    window.headerTimestamp = "2019/11/6 2:43:11";
    window.footerTimestamp = "2019/11/6 2:43:11";
    window.locFileTimestamp = "2019/11/6 2:43:05";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                loading...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-anomaly-detector" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a, .pure-content .technical-azure-selector p a, .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .speech-services-table tr, .speech-services-table td {
                            background-color: white;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/cognitive-slice-01.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/images/anomaly-detector.svg"/>
                                <h2>
                                    Azure AI Anomaly Detector
                                </h2>
                                <h4>
                                    Your business is only as reliable as its ability to detect problems
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Easily embed anomaly detection capabilities into your apps so users can quickly identify problems as soon as they occur. No machine-learning
                            background required. Through an API, Azure AI Anomaly Detector ingests time-series data of all types and selects the best-fitting anomaly
                            detection model for your data to ensure high accuracy. Customize the service to your business’s risk profile. Deploy it where you need it most
                            – only with Azure can you run Azure AI Anomaly Detector anywhere from the cloud to the intelligent edge.
                        </p>
                        <h2>
                            Pricing details
                        </h2>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure AI Anomaly Detector
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_cognitive-services">
                                                        Azure AI Anomaly Detector
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="Azure AI Anomaly Detector">
                                                Azure AI Anomaly Detector
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China North 3
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                        China East
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                        China North
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#north-china3" selected="selected" value="north-china3">
                                                China North 3
                                            </option>
                                            <option data-href="#east-china2" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content">
                            <!-- BEGIN: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 navigator 2 -->
                                <!-- BEGIN: Tab level 2 content 3 -->
                                <div class="tab-content">
                                    <p>
                                        The Free instance is free of charge with limited monthly quota up to 20K transactions and is great to get started with the service
                                        and to learn how to use it in your application.
                                    </p>
                                    <p>
                                        The Standard instance is designed for running production workloads. Pricing is based the number of transactions you make. A
                                        "transaction" is an API call with request payload size up to 1000 data points inclusive in the time series, each increment of 1K
                                        data points will add to another one transaction.
                                    </p>
                                    <table cellpadding="0" cellspacing="0" id="Anomaly_Detector" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                    INSTANCE
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    FEATURES
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    PRICE
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Free - Web/Container
                                            </td>
                                            <td>
                                                Univariate anomaly detection
                                            </td>
                                            <td>
                                                20000 transactions free per month
                                                <sup>
                                                    1
                                                </sup>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Standard - Web/Container
                                            </td>
                                            <td>
                                                Univariate anomaly detection
                                            </td>
                                            <td>
                                                ￥3.198 per 1,000 transactions
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="ms-date">
                                    <sup>
                                        1
                                    </sup>
                                    A "transaction" is an API call with request payload size up to 1000 data points inclusive in the time series, each increment of 1K
                                    data points will add to another one transaction.
                                </div>
                            </div>
                            <!-- END: TAB-CONTAINER-3 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="cognitive-services_question1">
                                            What constitutes a transaction for Azure AI Anomaly Detector API?
                                        </a>
                                        <section>
                                            <p>
                                                A transaction is an API call with request payload size up to 1000 data points inclusive in the time series, each increment
                                                of 1K data points will add to another one transaction. For example, an API call with request payload size == 2050 is 3
                                                transactions. The maximum request payload size is 8640 data points. Each data point in time series is a time
                                                stamp/numerical value pair.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="cognitive-services_question2">
                                            What happens if I exceed the transaction limit on my free tier for Azure AI Anomaly Detector?
                                        </a>
                                        <section>
                                            <p>
                                                Usage is throttled if the transaction limit is reached on the Free tier. Customers cannot accrue overages on the free
                                                tier.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="cognitive-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee that Azure AI Anomaly Detector will be available at least 99.9% of the time.No SLA is provided for the Free tier. If you want to
                            learn more about the details of our server level agreement, please visit the
                            <a href="/en-us/support/sla/cognitive-services/" id="pricing_cognitive-services_sla">
                                Service Level Agreement
                            </a>
                            page.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="TwAIjGHalcBGkqjWVj-SmjXBngZq-cmGfTisJrt1t3mUynLkxa5fI_1ChFbwKTYk9NXQS0cAJgGt0nieyquJ3RN7BT4nS5MgXxK4ArbGMF41" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
