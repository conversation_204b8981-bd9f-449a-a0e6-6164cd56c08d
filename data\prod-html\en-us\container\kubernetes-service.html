<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="free cloud, azure container service, Azure" name="keywords"/>
    <meta content="See pricing details for the Azure Kubernetes Service (AKS). No upfront costs. Pay as you go. FREE trial." name="description"/>
    <title>
        Pricing - Container Service - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/kubernetes-service" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "12/9/2019 7:37:32 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "11/6/2019 2:43:10 AM";
    window.footerTimestamp = "11/6/2019 2:43:10 AM";
    window.locFileTimestamp = "11/6/2019 2:43:04 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-kubernetes-service" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/kubernetes-service_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Azure Kubernetes Service (AKS)
                                </h2>
                                <h4>
                                    Highly available, secure, and fully managed Kubernetes service
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <h4>
                            Azure Kubernetes Service (AKS)
                        </h4>
                        <p>
                            Azure Kubernetes Service (AKS) is a free container service that simplifies the deployment, management, and operations of Kubernetes as a fully
                            managed Kubernetes container orchestrator service. Paying for only the virtual machines, and associated storage and networking resources
                            consumed makes AKS the most efficient and cost-effective container service on the market.
                        </p>
                    </div>

                    <table cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <th align="left">

                            </th>
                            <th align="left">
                                <strong>
                                    Free tier
                                </strong>
                            </th>
                            <th align="left">
                                <strong>
                                    Standard tier
                                </strong><br>
                                ￥0.636 per cluster per hour
                            </th>
                            <th align="left">
                                <strong>
                                    Premium tier
                                </strong><br>
                                ￥3.82 per cluster per hour
                            </th>
                        </tr>
                        <tr>
                            <td>
                                Recommended for
                            </td>
                            <td>
                                Learning, exploration, experimenting
                            </td>
                            <td>
                                All mission critical, at scale or production workloads
                            </td>
                            <td>
                                All mission critical, at scale or production workloads requiring 2 years of support
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Pricing for nodes
                            </td>
                            <td>
                                Only pay for what you use
                            </td>
                            <td>
                                Only pay for what you use
                            </td>
                            <td>
                                Only pay for what you use
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Cluster management
                            </td>
                            <td>
                                Free
                            </td>
                            <td>
                                1 Uptime SLA Hour
                            </td>
                            <td>
                                1 Premium Support Hour
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Service Level Agreement (SLA)
                            </td>
                            <td>
                                N/A
                            </td>
                            <td>
                                Financially backed API server uptime SLA
                            </td>
                            <td>
                                Financially backed API server uptime SLA
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Long Term Support
                            </td>
                            <td>
                                Community support
                            </td>
                            <td>
                                Community support
                            </td>
                            <td>
                                Microsoft maintenance past community support 
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Kubernetes Control Plane
                            </td>
                            <td>
                                No Autoscaling
                            </td>
                            <td>
                                API Server Autoscaling
                            </td>
                            <td>
                                API Server Autoscaling 
                            </td>
                        </tr>
                        <tr>
                            <td>
                                
                            </td>
                            <td>
                                Smaller control plane limits
                            </td>
                            <td>
                                Larger control plane limits
                            </td>
                            <td>
                                Larger control plane limits 
                            </td>
                        </tr>
                        <tr>
                            <td>
                                AKS Cluster Node limit
                            </td>
                            <td>
                                1,000 (not recommended at more than 10 node scale)
                            </td>
                            <td>
                                5,000
                            </td>
                            <td>
                                5,000 
                            </td>
                        </tr>
                    </table>
                    <div class="pricing-page-section">
                        <h4>
                            Free Cluster Management
                        </h4>
                        <p>
                            There is no charge for cluster management.
                        </p>
                    </div>
                    <div class="pricing-page-section">
                        <h4>
                            Pricing for nodes - only pay for what you use
                        </h4>
                        <p>
                            Only pay for the virtual machines instances, storage and networking resources consumed by your Kubernetes cluster.
                        </p>
                    </div>
                    <h4>
                        Option to add Uptime SLA
                    </h4>
                    <p>
                        Financially-backed service level agreement (SLA) that guarantees an uptime of 99.95% for the Kubernetes API server for clusters that use Azure
                        Availability Zone and 99.9% for clusters that do not use Azure Availability Zone.
                    </p>
                    <table cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <th align="left">
                                <strong>
                                    METER
                                </strong>
                            </th>
                            <th align="left">
                                <strong>
                                    PRICE
                                </strong>
                            </th>
                        </tr>
                        <tr>
                            <td>
                                Uptime SLA
                            </td>
                            <td>
                                ￥0.636 per cluster per hour
                            </td>
                        </tr>
                    </table>
                    <div class="tags-date">
                        <div class="ms-date">
                            Note：Billing for AKS uptime SLA will be effective start on May 1, 2021
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h4>
                            IP address options
                        </h4>
                        <p>
                            Every Azure Cloud service containing one or more Azure Virtual Machines is automatically assigned a free dynamic virtual IP (VIP) address. For
                            an additional charge, you can also get:
                        </p>
                        <ul>
                            <li>
                                Instance-level public IP addresses—A dynamic public IP address (PIP) that is assigned to a virtual machine for direct access.
                            </li>
                            <li>
                                Reserved IP addresses—A public IP address that can be reserved and used as a VIP address.
                            </li>
                            <li>
                                Load-balanced IP addresses—Additional load-balanced VIP addresses that can be assigned to an Azure Cloud Service containing one or more
                                Azure Virtual Machines.
                            </li>
                        </ul>
                        <p>
                            <a href="/en-us/pricing/details/ip-addresses">
                                See IP address pricing
                            </a>
                        </p>
                    </div>
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em id="ws_unfolded_all">
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="aks-que-q1">
                                            Why We have so much Azure Monitor cost after creating AKS?
                                        </a>
                                        <section>
                                            <p>
                                                Azure Monitor provide very powerful and useful logging and metrics for node and container metrics from AKS ,also result in
                                                some data ingestion costs. Please consider if Azure Monitor is needed when creating AKS and please disable Azure Monitor
                                                if unnecessary to avoid any cost. For more details ,please visit
                                                <a href="https://www.azure.cn/en-us/pricing/details/monitor/">
                                                    Azure Monitor Pricing Page
                                                </a>
                                                .
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            As a free service, AKS does not offer a financially-backed service level agreement. We will strive to attain at least 99.5% availability for
                            the Kubernetes API server. The availability of the agent nodes in your cluster is covered by the Virtual Machines SLA. Please see the
                            <a href="/en-us/support/sla/virtual-machines/">
                                Virtual Machines SLA
                            </a>
                            for more details.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="8z5H0CC5U3R6VVJyAtSJgT-8JYAQMaDYVk_zq8loZCZmYoynz1mAv-GM8hRoqxcI4d4VteTgThtwiwpefSsfYiWMI3wHK_aYyBsSUImiEec1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
