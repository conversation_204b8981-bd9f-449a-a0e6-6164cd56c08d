<!DOCTYPE html>
<html lang="en-US">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="Azure Network Watcher, Network, Virtual Network" name="keywords" />
    <meta
        content="Learn the pricing details of Azure Network Watcher. Network Watcher can be used to monitor and diagnose the operation status and performance of networks."
        name="description" />
    <title>
        Network Watcher Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="/Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/network-watcher/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="/Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="en-us">
    <script>
        window.requireUrlArgs = "1/6/2020 11:41:53 AM";
        window.currentLocale = "en-US";
        window.headerTimestamp = "5/9/2019 9:29:29 AM";
        window.footerTimestamp = "5/9/2019 9:29:29 AM";
        window.locFileTimestamp = "5/9/2019 9:29:21 AM";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="en-us-network-watcher" wacn.date="11/27/2015">
                        </tags>
                        <style type="text/css">
                            .pricing-detail-tab .tab-nav {
                                padding-left: 0 !important;
                                margin-top: 5px;
                                margin-bottom: 0;
                                overflow: hidden;
                            }

                            .pricing-detail-tab .tab-nav li {
                                list-style: none;
                                float: left;
                            }

                            .pricing-detail-tab .tab-nav li.active a {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-nav li.active a:hover {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel {
                                display: none;
                            }

                            .pricing-detail-tab .tab-content .tab-panel.show-md {
                                display: block;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                                padding-left: 5px;
                                padding-right: 5px;
                                color: #00a3d9;
                                background-color: #FFF;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pure-content .technical-azure-selector .tags-date a,
                            .pure-content .technical-azure-selector p a,
                            .pure-content .technical-azure-selector table a {
                                background: 0 0;
                                padding: 0;
                                margin: 0 6px;
                                height: 21px;
                                line-height: 22px;
                                font-size: 14px;
                                color: #00a3d9;
                                float: none;
                                display: inline;
                            }
                        </style>
                        <div class="hide-info" style="display:none;">
                            <div class="bg-box">
                                <div class="cover-bg">
                                </div>
                            </div>
                            <div class="msg-box">
                                <div class="pricing-unavailable-message">
                                    Not available in the selected region
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/network-watcher.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Images/marketing-resource/css/<EMAIL>" />
                                    <h2>
                                        Network Watcher
                                    </h2>
                                    <h4>
                                        Network performance monitoring and diagnostics solution
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <p>
                                Network Watcher can be used to monitor and diagnose the operation status and performance
                                of the network. For example,
                                the diagnose and visualization tools of Network Watcher can help capture data packages
                                on a Virtual Machine and
                                confirm whether to accept or refuse certain IP traffic. They can also mark and identify
                                locations to which data
                                packages will be routed from a Virtual Machine and deeply understand the whole network
                                topology.
                            </p>
                            <h2>
                                Pricing Details
                            </h2>
                        </div>
                        <!-- BEGIN: TAB-CONTROL -->
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                            <div class="tab-container-container">
                                <div class="tab-container-box">
                                    <div class="tab-container">
                                        <div class="dropdown-container software-kind-container" style="display:none;">
                                            <label>
                                                OS/Software:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    Network Watcher
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#tabContent1" href="javascript:void(0)"
                                                            id="home_network-watcher">
                                                            Network Watcher
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                                <option data-href="#tabContent1" selected="selected"
                                                    value="Network Watcher">
                                                    Network Watcher
                                                </option>
                                            </select>
                                        </div>
                                        <div class="dropdown-container region-container">
                                            <label>
                                                Region:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    China East 2
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li>
                                                        <a data-href="#north-china3" href="javascript:void(0)"
                                                            id="north-china3">
                                                            China North 3
                                                        </a>
                                                    </li>
                                                    <li class="active">
                                                        <a data-href="#east-china2" href="javascript:void(0)"
                                                            id="east-china2">
                                                            China East 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china2" href="javascript:void(0)"
                                                            id="north-china2">
                                                            China North 2
                                                        </a>
                                                    </li>
                                                     <li>
                                                        <a data-href="#east-china" href="javascript:void(0)"
                                                            id="east-china">
                                                            China East
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china" href="javascript:void(0)"
                                                            id="north-china">
                                                            China North
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                                <option data-href="#north-china3" value="north-china3">
                                                    China North 3
                                                </option>
                                                <option data-href="#east-china2" selected="selected"
                                                    value="east-china2">
                                                    China East 2
                                                </option>
                                                <option data-href="#north-china2" value="north-china2">
                                                    China North 2
                                                </option>
                                                <option data-href="#east-china" value="east-china">
                                                    China East
                                                </option>
                                                <option data-href="#north-china" value="north-china">
                                                    China North
                                                </option>
                                            </select>
                                        </div>
                                        <div class="clearfix">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- BEGIN: TAB-CONTAINER-1 -->
                            <div class="tab-content">
                                <!-- BEGIN: TAB-CONTAINER-3 -->
                                <div class="tab-panel" id="tabContent1">
                                    <!-- BEGIN: Tab level 2 navigator 2 -->
                                    <!-- BEGIN: Tab level 2 content 3 -->
                                    <div class="tab-content">
                                        <table cellpadding="0" cellspacing="0" id="network-watcher-region" width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        Feature
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Free Units Included
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Price
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Network Logs Collected
                                                    <sup>
                                                        1
                                                    </sup>
                                                </td>
                                                <td>
                                                    5 GB per month
                                                </td>
                                                <td>
                                                    ¥ 5.09/GB
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Network diagnostic tools
                                                    <sup>
                                                        2
                                                    </sup>
                                                </td>
                                                <td>
                                                    1,000 checks per month
                                                </td>
                                                <td>
                                                    ¥ 10.00/1,000 checks
                                                </td>
                                            </tr>
                                            <td>
                                                Connection Monitor
                                            </td>
                                            <td>
                                                10 tests per month
                                            </td>
                                            <td>
                                                0-10 tests - Included
                                                <br />
                                                10-240,010 tests - ￥3.054 per test per month
                                                <br />
                                                240,010-750,010 tests - ￥1.017 per test per month
                                                <br />
                                                750,010-1,000,010 tests - ￥0.509 per test per month
                                                <br />
                                                1,000,010+ tests - ￥0.203 per test per month
                                            </td>
                                        </table>
                                        <table cellpadding="0" cellspacing="0" id="network-watcher-region2"
                                            width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        Feature
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Free Units Included
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Price
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Network Logs Collected
                                                    <sup>
                                                        1
                                                    </sup>
                                                </td>
                                                <td>
                                                    5 GB per month
                                                </td>
                                                <td>
                                                    ¥ 5.09/GB
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Network diagnostic tools
                                                    <sup>
                                                        2
                                                    </sup>
                                                </td>
                                                <td>
                                                    1,000 checks per month
                                                </td>
                                                <td>
                                                    ¥ 10.00/1,000 checks
                                                </td>
                                            </tr>
                                            <td>
                                                Connection Monitor
                                            </td>
                                            <td>
                                                10 tests per month
                                            </td>
                                            <td>
                                                0-10 tests - Included
                                                <br />
                                                10-240,010 tests - ￥3.054 per test per month
                                                <br />
                                                240,010-750,010 tests - ￥1.017 per test per month
                                                <br />
                                                750,010-1,000,010 tests - ￥0.509 per test per month
                                                <br />
                                                1,000,010+ tests - ￥0.203 per test per month
                                            </td>
                                            <tr>
                                                <td>
                                                    Traffic Analytics
                                                </td>
                                                <td>
                                                    --
                                                </td>
                                                <td>
                                                    <p>
                                                        Data processing at 10-min interval:¥ 35.61 per GB-processed
                                                        <sup>
                                                            3
                                                        </sup>
                                                    </p>
                                                    <p>
                                                        Data Processing at 60-min interval:¥ 23.405 per GB-processed
                                                        <sup>
                                                            4
                                                        </sup>
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                        <table cellpadding="0" cellspacing="0" id="network-watcher-region3"
                                            width="100%">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                        Feature
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Free Units Included
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        Price
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Network flow logs collected (NSG flow logs)
                                                    <sup>1</sup>
                                                </td>
                                                <td>
                                                    5 GB per month
                                                </td>
                                                <td>
                                                    ￥5.09 per GB
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Virtual network flow logs collected
                                                </td>
                                                <td>
                                                    N/A
                                                </td>
                                                <td>
                                                    ￥3.18 per GB
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Network Diagnostic Tools
                                                    <sup>2</sup>
                                                </td>
                                                <td>
                                                    1,000 checks per month
                                                </td>
                                                <td>
                                                    ￥10.00/1,000 per check
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Connection Monitor
                                                </td>
                                                <td>
                                                    10 tests per month
                                                </td>
                                                <td>
                                                    0-10 tests - Included
                                                    <br />
                                                    10-240,010 tests - ￥3.054 per test per month
                                                    <br />
                                                    240,010-750,010 tests - ￥1.017 per test per month
                                                    <br />
                                                    750,010-1,000,010 tests - ￥0.509 per test per month
                                                    <br />
                                                    1,000,010+ tests - ￥0.203 per test per month
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    Traffic Analytics
                                                </td>
                                                <td>
                                                    ‐ ‐
                                                </td>
                                                <td>
                                                    Accelerated processing at 10-min intervals: ￥35.61 per GB-processed
                                                    <br>
                                                    Standard processing at 60-min intervals: ￥23.405 per GB-processed
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                <sup>
                                                    1
                                                </sup>
                                                Network log is stored in the Storage account, and 1-day to 365-day
                                                retention policies
                                                are provided. If you do not configure a retention policy, the logs will
                                                be retained all the time. Relevant fees will
                                                be charged for Storage, Log Analytics and Event Hubs.
                                            </div>
                                            <br />
                                            <div class="ms-date">
                                                <sup>
                                                    2
                                                </sup>
                                                The billing for the diagnostic tools and topological features of Network
                                                Watcher is
                                                based on the number of network diagnosis triggered by Azure portal,
                                                PowerShell, CLI or Rest.
                                            </div>
                                            <br />
                                            <div class="ms-date">
                                                <sup>
                                                    3
                                                </sup>
                                                Data Processing at 10-min interval full pricing will be effective on
                                                January
                                                1
                                                <sup>
                                                    st
                                                </sup>
                                                , 2020.
                                            </div>
                                            <br />
                                            <div class="ms-date">
                                                <sup>
                                                    4
                                                </sup>
                                                Traffic Analytics feature is billed for log data processed by the
                                                service and for the
                                                resulting data ingested into the Azure Log Analytics service.
                                                Corresponding charges apply for logs ingested to the
                                                <a href="/en-us/pricing/details/log-analytics/">
                                                    Azure Log Analytics
                                                </a>
                                                service.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END: TAB-CONTAINER-3 -->
                            </div>
                        </div>
                        <!-- END: TAB-CONTROL -->
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <h2>
                                Support &amp; SLA
                            </h2>
                            <p>
                                If you have any questions or need help, please visit
                                <a href="https://support.azure.cn/en-us/support/contact"
                                    id="network-watcher-contact-page">
                                    Azure Support
                                </a>
                                and select self-help service or any other method to contact us for support.
                            </p>
                            <p>
                                We guarantee that at least 99.9% of the time, network diagnostic tools can successfully
                                perform and return responses. To
                                learn more about the details of our Service Level Agreement, please visit the
                                <a href="/en-us/support/sla/network-watcher/index.html"
                                    id="pricing_network-watcher-watcher_sla">
                                    Service Level Agreements
                                </a>
                                page.
                            </p>
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                   -->
                        <!--END: Support and service code chunk-->
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="/Static/CSS/Localization/en-us.css" rel="stylesheet" />
    <script src="/Static//Scripts/lib/jquery-1.12.3.min.js">
    </script>
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="zGHQKqCz7Ov6ZgeOZ57E23jjn94JCLQHO6C3p7miDjWn3sUzUmI_uF9Ih9WNQl-flH5QKKfATm4naO674143RJBQcvRrEQzXQh6isCCzoXY1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="/Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>