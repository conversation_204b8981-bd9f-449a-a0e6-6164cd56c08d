<!DOCTYPE html>
<html lang="en-US">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta
        content="Azure Microsoft Cloud Network Virtual Network Virtual Network VNet Virtual Private Network, Site-to-Site, VPN"
        name="keywords" />
    <meta
        content="Learn about the pricing details of the Azure virtual network. Azure virtual network is free of charge. Every subscription permits building at most 50 virtual networks across all regions."
        name="description" />
    <title>
        Virtual Network Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="/Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/virtual-network/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="/Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="en-us">
    <script>
        window.requireUrlArgs = "1/6/2020 11:41:53 AM";
        window.currentLocale = "en-US";
        window.headerTimestamp = "5/9/2019 9:29:29 AM";
        window.footerTimestamp = "5/9/2019 9:29:29 AM";
        window.locFileTimestamp = "5/9/2019 9:29:21 AM";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="en-us-virtual-network" wacn.date="11/27/2015">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/virtual_network.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Images/marketing-resource/css/<EMAIL>" />
                                    <h2>
                                        Virtual Network
                                        <!--span>Virtual Network</span-->
                                    </h2>
                                    <h4>
                                        Provision private networks, optionally connect to on-premises datacenters
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <p>
                                The virtual network in Azure is free of charge. Every subscription permits building up
                                tp 50 virtual networks across
                                all regions.
                            </p>
                            <p>
                                The public IP address and retained IP address used by services in virtual networks are
                                charged.
                            </p>
                            <p>
                                The network appliances including VPN gateway and Application Gateway running in the
                                Virtual Network are also charged.
                            </p>

                            <h2>
                                Virtual network peering
                            </h2>
                            <p>
                                Virtual network peering links virtual networks, enabling you to route traffic between
                                them using private IP
                                addresses. Ingress and egress traffic is charged at both ends of the peered networks.
                            </p>
                            <h2>
                                VNET Peering within the same region
                            </h2>
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br />
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Metering
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Price
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Inbound data transfer
                                    </td>
                                    <td>
                                        ￥0.06572 /GB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Outbound data transfer
                                    </td>
                                    <td>
                                        ￥0.06572 /GB
                                    </td>
                                </tr>
                            </table>
                            <h2>
                                Global VNET Peering
                            </h2>
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br />
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            ZONE 1
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            ZONE 2
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Inbound data transfer
                                    </td>
                                    <td>
                                        ￥0.1638 /GB
                                    </td>
                                    <td>
                                        ￥0.1638 /GB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Outbound data transfer
                                    </td>
                                    <td>
                                        ￥0.1638 /GB
                                    </td>
                                    <td>
                                        ￥0.1638 /GB
                                    </td>
                                </tr>
                            </table>
                            <p>
                                <sup>
                                    *
                                </sup>
                                Global VNET Peering pricing is based on a zonal structure. For instance, if data is
                                being transferred
                                from a VNET in zone 1 to a VNET in zone 2, customers will incur outbound data transfer
                                rates for zone 1 and inbound
                                data transfer rates for zone 2.
                            </p>
                            <h2>
                                Virtual Network NAT
                            </h2>
                            <p>
                                Virtual Network NAT (network address translation) simplifies outbound-only Internet
                                connectivity for virtual networks and is fully managed and
                                highly resilient. NAT gateway is billed with duration of NAT Gateway exists and all
                                traffic processed by NAT Gateway.
                            </p>
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br />
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Price
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        NAT Gateway
                                    </td>
                                    <td>
                                        ￥0.458 per gateway per hour
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Data Processed
                                    </td>
                                    <td>
                                        ￥0.458 per GB
                                    </td>
                                </tr>
                            </table>
                            <!-- <div class="tags-date">
                                    <div class="ms-date"><sup>*</sup>Billing will start on June 1, 2020.</div>
                                </div> -->

                            <h2>
                                IP Address
                            </h2>
                            <p>
                                The public IP address and reserved IP address can be applied to services running in the
                                virtual network. The
                                <a href="/en-us/pricing/details/reserved-ip-addresses/" id="virtual_network_price_here"
                                    style="color: #00a8d9;" target="_blank">
                                    Public IP Address Pricing Details
                                </a>
                                page lists the standard
                                fees of these IP addresses.
                            </p>

                            <h2>
                                VPN Gateway
                            </h2>
                            <p>
                                The virtual network may have one or several VPN gateways of the on-premises networks or
                                other virtual networks
                                connected back to Azure.
                            </p>
                            <p>
                                For the charging standards of VPN gateways, see the
                                <a class="link-btn" href="/en-us/pricing/details/vpn-gateway/"
                                    id="virtual_network_price_detail" style="color: #00a8d9;" target="_blank">
                                    VPN Gateway
                                    Pricing Details
                                </a>
                                page.
                            </p>
                        </div>
                        <div class="pricing-page-section">
                            <div class="more-detail">
                                <h2>
                                    FAQ
                                </h2>
                                <em>
                                    Expand All
                                </em>
                                <ul>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="vnetwork-que-1">
                                                Will the pricing differ by region or zone?
                                            </a>
                                            <section>
                                                <p>
                                                    For Global VNET Peering pricing will differ based on the zone your
                                                    VNETs are in.
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="vnetwork-que-2">
                                                Will there be a charge for data transfer within a virtual network?
                                            </a>
                                            <section>
                                                <p>
                                                    No, there is no charge for data transfer within a virtual network
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="vnetwork-que-3">
                                                Will there be any compute charges in addition to virtual network peering
                                                charges?
                                            </a>
                                            <section>
                                                <p>
                                                    No, you pay for other resources as you normally would. Neither VNET
                                                    Peering, nor Global VNET peering
                                                    impose any compute charges.
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="vnetwork-que-4">
                                                How does billing for this service work?
                                            </a>
                                            <section>
                                                <p>
                                                    VNET Peering is billed based on the ingress and egress data being
                                                    transferred from one VNET to another.
                                                </p>
                                                <p>
                                                    Global Peering, like VNET peering, is billed based on ingress and
                                                    egress data transfer. However, the
                                                    pricing differs based on the zone the region is in.
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="vnetwork-que-5">
                                                Which regions correspond to Zone 1, Zone 2?
                                            </a>
                                            <section>
                                                <p>
                                                    A sub-region is the lowest level geo-location that you may select to
                                                    deploy your applications and
                                                    associated data. For data transfers (except CDN), the following
                                                    regions correspond to Zone 1 and Zone 2:
                                                </p>
                                                <p>
                                                    Zone 1: CN East,CN East 3, CN North and CN North 3
                                                </p>
                                                <p>
                                                    Zone 2: CN East 2 and CN North 2
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="pricing-page-section">
                            <h2>
                                Support &amp; SLA
                            </h2>
                            <p>
                                If you have any questions or need help, please visit
                                <a href="https://support.azure.cn/en-us/support/contact" id="networking-contact-page">
                                    Azure Support
                                </a>
                                and select self-help service or any other method to contact us for support.
                            </p>
                            <p>
                                The virtual network is free of charge and doesn’t provide service level agreements. To
                                learn more about the details of
                                our service level agreement, please visit the
                                <a href="/en-us/support/legal/sla/index.html" id="pricing_virtual-networking_sla">
                                    Service Level Agreements
                                </a>
                                page.
                            </p>
                            <!--
                                <p>对于虚拟网络，我们保证虚拟网络网关的可用性不低于 99.9%。若要了解有关我们的服务器级别协议的详细信息，请访问<a href="/en-us/support/sla/virtual-networking/" id="pricing_virtual-networking_sla">Service Level Agreements</a> page.</p>
                                -->
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="/Static/CSS/Localization/en-us.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="n-8gfeiA97pAX-AELJHZwD1RQGH814VzuHgQ6FUfXcpOkTaroTlseAC2kffGOHZ0epOKzYp4zChvnWj1YzOR5dwinR5Fe68jpieyju_7GX01" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="/Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>