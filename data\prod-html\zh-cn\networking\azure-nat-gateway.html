<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, 网络，虚拟网络，Virtual Network，VNet，虚拟专用网络，站点到站点 ，VPN，IP 地址，DNS，IPSec，免费" name="keywords"/>
  <meta content="了解 Azure 虚拟网络（Virtual Network）价格详情。Azure 虚拟网络是免费的。每个订阅可以跨所有区域创建最多 50 个虚拟网络。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   VPN虚拟专用网络价格预算 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/virtual-network/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="nat-gateway" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/virtual_network.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           Azure NAT 网关
           <span>
            Azure NAT Gateway
           </span>
          </h2>
          <h4>
            简化虚拟网络的出站 Internet 连接
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
            NAT 网关是一种完全托管的服务，可安全地从具有企业级性能和低延迟的专用虚拟网络路由 Internet 流量。NAT 网关使用软件定义的网络提供内置的高可用性。使用 NAT 网关轻松配置、缩放和部署动态工作负载的出站连接。
        </p>
       </div>
       
       <div class="pricing-page-section">
        <h2>
         虚拟网络 NAT
        </h2>
        <p>
         虚拟网络 NAT（网络地址转换）简化了虚拟网络的仅限出站 的网络连接。NAT是完全托管式的，且具有很高的复原能力。NAT 通过NAT 网关资源的存在持续时间及NAT 网关资源处理的所有流量单独计费。
        </p>
        <table cellpadding="0" cellspacing="0" width="100%">
         <tr>
          <th align="left">
           <strong>
           </strong>
          </th>
          <th align="left">
           <strong>
            价格
           </strong>
          </th>
         </tr>
         <tr>
          <td>
           NAT
          </td>
          <td>
           ￥0.458 /网关/小时
          </td>
         </tr>
         <tr>
          <td>
           数据处理
          </td>
          <td>
           ￥0.458 /GB
          </td>
         </tr>
        </table>
       </div>

       <!-- <div class="pricing-page-section">
        <h2>
         IP 地址
        </h2>
        <p>
         公共 IP 地址和保留 IP 地址可用于虚拟网络中正在运行的服务。
         <a href="../reserved-ip-addresses/index.html" id="virtual_network_price_here" style="color: #00a8d9;" target="_blank">
          公共 IP 地址价格详情
         </a>
         页面列出了这些 IP 地址的标准费用。
        </p>
       </div> -->
       <!-- <div class="pricing-page-section">
        <h2>
         VPN 网关
        </h2>
        <p>
         虚拟网络可以具有一个或多个连接回 Azure 中的本地网络或其他虚拟网络的 VPN 网关。
        </p>
        <p>
         VPN 网关的收费标准详见
         <a class="link-btn" href="../vpn-gateway/index.html" id="virtual_network_price_detail" style="color: #00a8d9;" target="_blank">
          VPN 网关价格详情
         </a>
         页面。
        </p>
       </div> -->
      
       <!-- <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="vnetwork-que-1">
             地区或区域间的定价有所不同吗？
            </a>
            <section>
             <p>
              根据 VNET 所在区域不同，全局 VNET 对等互连的定价会有差别。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="vnetwork-que-2">
             虚拟网络内的数据传输要收取费用吗？
            </a>
            <section>
             <p>
              不。虚拟网络内的数据传输不收取任何费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="vnetwork-que-3">
             除了虚拟网络对等互连费用之外，还有其他任何计算费用吗？
            </a>
            <section>
             <p>
              不。你可像往常一样为其他资源付费。VNET 对等互连和全局 VNET 对等互连都不会收取任何计算费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="vnetwork-que-4">
             此服务如何计费？
            </a>
            <section>
             <p>
              VNET 对等互连计费方式以从一个 VNET 传输到另一个 VNET 的入口和出口数据为准。
             </p>
             <p>
              全局对等互连（例如 VNET 对等互连）计费方式以入口和出口数据传输为准。但是定价根据所在区域不同而有所差异。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="vnetwork-que-5">
             哪些地区与区域 1、区域 2 相对应？
            </a>
            <section>
             <p>
              子地区是可选择部署应用程序和关联数据的最低级别的地理位置。对于数据传输（CDN 除外），以下地区与区域 1、区域 2 相对应：
             </p>
             <p>
              区域 1：中国东部，中国东部 3，中国北部，中国北部 3
             </p>
             <p>
              区域 2：中国东部 2 和中国北部 2
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div> -->
       <!-- <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="networking-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         虚拟网络是免费的，不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/legal/sla/index.html" id="pricing_virtual-networking_sla">
          服务级别协议
         </a>
         页。
        </p>
    
       </div> -->
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="n-8gfeiA97pAX-AELJHZwD1RQGH814VzuHgQ6FUfXcpOkTaroTlseAC2kffGOHZ0epOKzYp4zChvnWj1YzOR5dwinR5Fe68jpieyju_7GX01" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
