<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Azure Active Directory, directory services pricing" name="keywords"/>
    <meta content="AAD currently offers a free level. The free version of AAD covers cloud application access and self-service identity management for task workers with priority requirements for the cloud."
          name="description"/>
    <title>
        Microsoft Entra Domain Services
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/analysis-services/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "en-US";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-active-directory-ds" wacn.date="11/27/2015">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector .tags-date a,
                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .svg {
                            width: 50px;
                            float: left;
                            margin-right: 10px;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_analysis-services.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <div class="svg">
                                    <svg aria-hidden="true" data-slug-id="active-directory" role="presentation" viewbox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                                        <defs>
                                            <lineargradient gradientunits="userSpaceOnUse" id="active-directory:ba2610c3-a45a-4e7e-a0c0-285cfd7e005d-113a67d0" x1="13.25"
                                                            x2="8.62" y1="13.02" y2="4.25">
                                                <stop offset="0" stop-color="#1988d9">
                                                </stop>
                                                <stop offset="0.9" stop-color="#54aef0">
                                                </stop>
                                            </lineargradient>
                                            <lineargradient gradientunits="userSpaceOnUse" id="active-directory:bd8f618b-4f2f-4cb7-aff0-2fd2d211326d-9e38e0da" x1="11.26"
                                                            x2="14.46" y1="10.47" y2="15.99">
                                                <stop offset="0.1" stop-color="#54aef0">
                                                </stop>
                                                <stop offset="0.29" stop-color="#4fabee">
                                                </stop>
                                                <stop offset="0.51" stop-color="#41a2e9">
                                                </stop>
                                                <stop offset="0.74" stop-color="#2a93e0">
                                                </stop>
                                                <stop offset="0.88" stop-color="#1988d9">
                                                </stop>
                                            </lineargradient>
                                        </defs>
                                        <polygon fill="#50e6ff" points="1.01 10.19 8.93 15.33 16.99 10.17 18 11.35 8.93 17.19 0 11.35 1.01 10.19">
                                        </polygon>
                                        <polygon fill="#fff" points="1.61 9.53 8.93 0.81 16.4 9.54 8.93 14.26 1.61 9.53">
                                        </polygon>
                                        <polygon fill="#50e6ff" points="8.93 0.81 8.93 14.26 1.61 9.53 8.93 0.81">
                                        </polygon>
                                        <polygon fill="url(#active-directory:ba2610c3-a45a-4e7e-a0c0-285cfd7e005d-113a67d0)"
                                                 points="8.93 0.81 8.93 14.26 16.4 9.54 8.93 0.81">
                                        </polygon>
                                        <polygon fill="#53b1e0" points="8.93 7.76 16.4 9.54 8.93 14.26 8.93 7.76">
                                        </polygon>
                                        <polygon fill="#9cebff" points="8.93 14.26 1.61 9.53 8.93 7.76 8.93 14.26">
                                        </polygon>
                                        <polygon fill="url(#active-directory:bd8f618b-4f2f-4cb7-aff0-2fd2d211326d-9e38e0da)"
                                                 points="8.93 17.19 18 11.35 16.99 10.17 8.93 15.33 8.93 17.19">
                                        </polygon>
                                    </svg>
                                </div>
                                <h2>
                                    Microsoft Entra Domain Services
                                </h2>
                                <h4>
                                    Domain services for virtual machines and directory-aware applications
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Microsoft Entra Domain Services provides scalable, high-performance, managed
                            domain services such as domain-join, LDAP, Kerberos, Windows Integrated authentication,
                            and group policy. With the click of a button, administrators can enable managed domain
                            services for virtual machines and directory-aware applications deployed in Azure
                            Infrastructure Services. By maintaining compatibility with Windows Server Active
                            Directory, Microsoft Entra Domain Services allows administrators to easily
                            migrate legacy on-premises applications to the cloud and to centralize management of all
                            applications and all identities in Azure Active Directory.
                        </p>
                        <h2>
                            Pricing Details
                        </h2>
                        <p>
                            Microsoft Entra Domain Services usage is charged per hour, based on the SKU
                            selected by the tenant owner. Azure Active Directory is available in User Forest and
                            Resource Forest.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Analysis Services
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_analysis-services">
                                                        Analysis Services
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="Active Directory">
                                                Analysis Services
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China East 2
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#east-china2" selected="selected" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- BEGIN: TAB-CONTAINER-1 -->
                        <div class="tab-content">
                            <!-- BEGIN: TAB-CONTAINER-3 -->
                            <div class="tab-panel" id="tabContent1">
                                <!-- BEGIN: Tab level 2 navigator 2 -->
                                <!-- BEGIN: Tab level 2 content 3 -->
                                <div class="tab-content">
                                    <!-- BEGIN: Table1-Content-->
                                    <table cellpadding="0" cellspacing="0" id="active-directory-standard-tier-region" width="100%">
                                        <thead>
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    STANDARD
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    ENTERPRISE
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    PREMIUM
                                                </strong>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td class="left_align">
                                                <strong>
                                                    AAD DS Core Service
                                                </strong>
                                            </td>
                                            <td class="left_align">
                                            </td>
                                            <td class="left_align">
                                            </td>
                                            <td class="left_align">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                Suggested Auth Load (peak, per
                                                hour)
                                                <sup>
                                                    1
                                                </sup>
                                            </td>
                                            <td class="left_align">
                                                0 to 3,000
                                            </td>
                                            <td class="left_align">
                                                3,000 to 10,000
                                            </td>
                                            <td class="left_align">
                                                10,000 to 70,000
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                Suggested Object Count
                                                <sup>
                                                    2
                                                </sup>
                                            </td>
                                            <td class="left_align">
                                                0 to 25,000
                                            </td>
                                            <td class="left_align">
                                                25,000 to 100,000
                                            </td>
                                            <td class="left_align">
                                                100,000 to 500,000
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                Backup Frequency
                                            </td>
                                            <td class="left_align">
                                                Every 5 Days
                                            </td>
                                            <td class="left_align">
                                                Every 3 Days
                                            </td>
                                            <td class="left_align">
                                                Daily
                                                <sup>
                                                    3
                                                </sup>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                Resource Forest Trusts
                                            </td>
                                            <td class="left_align">
                                                N/A
                                            </td>
                                            <td class="left_align">
                                                5
                                            </td>
                                            <td class="left_align">
                                                10
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                <strong>
                                                    Instances
                                                </strong>
                                            </td>
                                            <td class="left_align">
                                            </td>
                                            <td class="left_align">
                                            </td>
                                            <td class="left_align">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                Resource Forest
                                            </td>
                                            <td class="left_align">
                                                N/A
                                            </td>
                                            <td class="left_align">
                                                ￥4.07/hour
                                            </td>
                                            <td class="left_align">
                                                ￥16.282/hour
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_align">
                                                User Forest
                                                <sup>
                                                    4
                                                </sup>
                                            </td>
                                            <td class="left_align">
                                                ￥1.526/hour
                                            </td>
                                            <td class="left_align">
                                                ￥4.07/hour
                                            </td>
                                            <td class="left_align">
                                                ￥16.282/hour
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <p>
                                        <sup>
                                            1
                                        </sup>
                                        Transactions are given as guidelines for selecting SKU and are
                                        not SLA. Directory performance may vary depending on the needs of your
                                        applications and amount of authentication requests.
                                    </p>
                                    <p>
                                        <sup>
                                            2
                                        </sup>
                                        Object count is given as a guideline for selecting SKU and not
                                        limited in the product.
                                    </p>
                                    <p>
                                        <sup>
                                            3
                                        </sup>
                                        Daily backups will be retained 7 days, with every 3rd backup
                                        being retained 30 days.
                                    </p>
                                    <p>
                                        <sup>
                                            4
                                        </sup>
                                        Each instance consists of 2 domain controllers for high availability, spread across 2 availability zones (if available in region).
                                    </p>
                                </div>
                            </div>
                            <!-- END: TAB-CONTAINER-3 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            We provide technical support for Microsoft Entra Domain Services. Billing and
                            subscription management support is provided at no cost.
                        </p>
                        <p>
                            SLA — We guarantee at least 99.9% of Microsoft Entra Domain Services requests for
                            domain authentication of user accounts belonging to the managed domain, LDAP bind to the
                            root DSE, or DNS lookup of records will complete successfully.
                            <a href="/en-us/support/sla/active-directory-ds/v1_0" id="pricing_adds_sla">
                                Learn more
                                about our SLA
                            </a>
                            . This SLA does not apply to the Resource Forest Enterprise while
                            in preview.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!-- END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static//Scripts/lib/jquery-1.12.3.min.js">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="rO62GKiRoQZVwCXZcP5uzFHDzhB5TOwgTP-cUG5fOOY894KJpbP_TOQXfijNrylLG_IhjL5ieAQQiFVTchQwr-B9xCfajtLnWYViaK3JJBg1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
