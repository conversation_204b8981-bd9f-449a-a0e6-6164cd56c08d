<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Azure Firewall, Pricing" name="keywords"/>
    <meta content="Pricing details page for Azure Firewall, a cloud-native network security and analytics service." name="description"/>
    <title>
        Azure Firewall – Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/azure-firewall/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }

    .ul {
        overflow: hidden;
        background: #f4f5f6;
        padding: 40px 20px;
    }

    .ul > li {
        list-style: none;
        display: block;
        width: 50%;
        float: left;
        padding: 10px;
    }
</style>
<div class="hide-info" style="display:none;">
    <div class="bg-box">
        <div class="cover-bg">
        </div>
    </div>
    <div class="msg-box">
        <div class="pricing-unavailable-message">
            Not available in the selected region
        </div>
    </div>
</div>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-azure-firewall" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/azure-firewall_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/azure-firewall.svg"/>
                                <h2>
                                    Azure Firewall
                                    <span>
            Azure Firewall
           </span>
                                </h2>
                                <h4>
                                    Cloud-native network security to protect your Azure Virtual Network resources
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <ul class="ul">
                        <li>
                            <h2>
                                Native firewalling capabilities with built-in high availability, unrestricted cloud scalability, and zero maintenance
                            </h2>
                        </li>
                        <li>
                            <p>
                                Azure Firewall is a managed cloud-based network security service that protects your Azure Virtual Network resources. Azure Firewall can be
                                seamlessly deployed, requires zero maintenance, and is highly available with unrestricted cloud scalability. Setting up an Azure Firewall
                                is easy; with billing comprised of a fixed and variable fee.
                            </p>
                        </li>
                    </ul>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                        <div class="tab-container-container">
                            <div class="tab-container-box">
                                <div class="tab-container">
                                    <div class="dropdown-container software-kind-container" style="display:none;">
                                        <label>
                                            OS/Software:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure Firewall
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#tabContent1" href="javascript:void(0)" id="home_Azure_azure-firewall">
                                                        Azure Firewall
                                                    </a>
                                                </li>
                                            </ol>
                                        </div>
                                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                            <option data-href="#tabContent1" selected="selected" value="azure-firewall">
                                                Azure Firewall
                                            </option>
                                        </select>
                                    </div>
                                    <div class="dropdown-container region-container">
                                        <label>
                                            Region:
                                        </label>
                                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              China East 2
             </span>
                                            <i class="icon">
                                            </i>
                                            <ol class="tab-items">
                                                <li class="active">
                                                    <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                        China North 3
                                                    </a>
                                                </li>                                                 
                                                <li>
                                                    <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                        China East 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                        China North 2
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                        China East
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                        China North
                                                    </a>
                                                </li>                                               
                                            </ol>
                                        </div>
                                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                            <option data-href="#north-china3" selected="selected" value="north-china3">
                                                China North 3
                                            </option>                                             
                                            <option data-href="#east-china2" value="east-china2">
                                                China East 2
                                            </option>
                                            <option data-href="#north-china2" value="north-china2">
                                                China North 2
                                            </option>
                                            <option data-href="#east-china" value="east-china">
                                                China East
                                            </option>
                                            <option data-href="#north-china" value="north-china">
                                                China North
                                            </option>                                           
                                        </select>
                                    </div>
                                    <div class="clearfix">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-content">
                            <!-- BEGIN: TAB-CONTAINER-1 -->
                            <div class="tab-control-container tab-active" id="tabContent1">
                                <!-- BEGIN: Table1-Content-->
                                <div class="scroll-talbe" style="display:block;">
                                    <h2>
                                        Azure Firewall
                                    </h2>
                                    <table cellpadding="0" cellspacing="0" id="azure_firewall_standard" width="100%">
                                        <tr>
                                            <th align="left" style="width: 384px;">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Basic
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Standard
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Premium
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Deployment
                                            </td>
                                            <td>
                                                ￥4.02 per deployment hour
                                            </td>
                                            <td>
                                                ￥12.72 per deployment hour
                                            </td>
                                            <td>
                                                ￥17.808 per deployment hour
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Data Processing
                                            </td>
                                            <td>
                                                ￥0.66 per GB processed
                                            </td>
                                            <td>
                                                ￥0.1632 per GB processed
                                            </td>
                                            <td>
                                                ￥0.1632 per GB processed
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <!-- <div class="tags-date">
                                                  <div class="ms-date"><sup>*</sup>At Public Preview, price is discounted at 50% on the premium SKU</div><br>
                                              </div> -->
                                <div class="scroll-talbe" style="display:block;">
                                    <h2>
                                        Azure Firewall with Secured Virtual Hub
                                    </h2>
                                    <table cellpadding="0" cellspacing="0" id="azure_firewall_standard2" width="100%">
                                        <tr>
                                            <th align="left" style="width: 384px;">
                                                <strong>
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Basic
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Standard
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    Premium
                                                </strong>
                                            </th>
                                            <!-- <th align="left"><strong>Premium*</strong></th> -->
                                        </tr>
                                        <tr>
                                            <td>
                                                Secured Virtual Hubs Deployments
                                            </td>
                                            <td>
                                                ￥2.5122 per deployment hour
                                            </td>
                                            <td>
                                                ￥12.72 per deployment hour
                                            </td>
                                            <td>
                                                ￥17.808 per deployment hour
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Secured Virtual Hubs Data Processed
                                            </td>
                                            <td>
                                                ￥0.4134 per GB processed
                                            </td>
                                            <td>
                                                ￥0.1632 per GB processed
                                            </td>
                                            <td>
                                                ￥0.1632 per GB processed
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <!-- <table cellpadding="0" cellspacing="0" width="100%" id="azure_firewall_standard3">
                                                  <tr>
                                                      <th align="left" style="width: 384px;"><strong>&nbsp;</strong></th>
                                                      <th align="left"><strong>Standard</strong></th>
                                                      <th align="left" style="width: 430px;"><strong>Premium</strong></th>
                                                  </tr>
                                                  <tr>
                                                      <td>Secured Virtual Hubs Deployments</td>
                                                      <td>￥12.72 per deployment hour</td>
                                                      <td>N/A</td>
                                                  </tr>
                                                  <tr>
                                                      <td>Secured Virtual Hubs Data Processed</td>
                                                      <td>￥0.1632 per GB processed</td>
                                                      <td>N/A</td>
                                                  </tr>
                                              </table> -->
                                <!-- <div class="tags-date">
                                                  <div class="ms-date"><sup>*</sup>At Public Preview, price is discounted at 50% on the premium SKU</div><br>
                                              </div> -->
                            </div>
                            <!-- END: TAB-CONTAINER-1 -->
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQs
                            </h2>
                            <em>
                                Expand all
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="ih-que-q1">
                                            Is a partial hour billed as a full hour?
                                        </a>
                                        <section>
                                            <p>
                                                Yes. A partial hour is billed as a full hour.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="ih-que-q2">
                                            Will there be any compute charges in addition to Azure Firewall charges?
                                        </a>
                                        <section>
                                            <p>
                                                No, you pay for other resources as you normally would. Azure Firewall will not impose any compute charges.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="ih-que-q3">
                                            How does billing for this service work?
                                        </a>
                                        <section>
                                            <p>
                                                A fixed hourly fee will be charged per a firewall deployment regardless of scale. In addition, data processing
                                                fee is billed per deployment for any data processed by your firewall.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact/" id="iot-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            We guarantee that Azure Firewall will be available at least 99.95% of the time. To learn more about the details of our
                            Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/azure-firewall/index.html" id="pricing_azure-firewall_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                     -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="-CASFLGdhrCi6YtrogWFdoAjf8BLjr1fwSl-4fTUaPTWaWereL1kW_15FQNSWbW6cImrTeoS1RToEkNBInbw1cYsESKFfYYFJfRt8toKicc1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
