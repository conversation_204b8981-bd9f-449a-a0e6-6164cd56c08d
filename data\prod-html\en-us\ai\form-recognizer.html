<!DOCTYPE html>
<html lang="en-Us">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="Pricing details for Azure AI Document Intelligence, a text and data extraction API from Azure Cognitive Services. Pay for the plan that best fits your needs."
          name="description"/>
    <meta content="Azure AI Document Intelligence pricing, text data extraction api" name="keywords"/>
    <title>
        Azure AI Document Intelligence pricing
    </title>
    <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "en-US";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                loading...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="10/19/2020" ms.service="en-us-form-recognizer" wacn.date="10/19/2020">
                    </tags>
                    <style type="text/css">
                        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .svg {
                            width: 50px;
                            float: left;
                            margin-right: 10px;
                        }

                        .sub_list {
                            overflow: hidden;
                            background: #f4f5f6;
                            padding: 20px 0;
                        }

                        .sub_list > li {
                            display: block;
                            list-style: none;
                            float: left;
                            width: 50%;
                        }
                    </style>
                    <div class="hide-info" style="display:none;">
                        <div class="bg-box">
                            <div class="cover-bg">
                            </div>
                        </div>
                        <div class="msg-box">
                            <div class="pricing-unavailable-message">
                                Not available in the selected region
                            </div>
                        </div>
                    </div>
                    <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <div class="svg">
                                    <svg aria-hidden="true" data-slug-id="form-recognizer" role="presentation" viewbox="0 0 61 61" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M36.7376 32.8516H33.9688V35.6204H36.7376V32.8516Z" fill="#0078D4">
                                        </path>
                                        <path d="M36.7376 37.8594H33.9688V40.6282H36.7376V37.8594Z" fill="#0078D4">
                                        </path>
                                        <path d="M41.2883 32.8516H38.5195V35.6204H41.2883V32.8516Z" fill="#0078D4">
                                        </path>
                                        <path d="M41.2883 37.8594H38.5195V40.6282H41.2883V37.8594Z" fill="#0078D4">
                                        </path>
                                        <path d="M45.7649 32.8516H42.9961V35.6204H45.7649V32.8516Z" fill="#0078D4">
                                        </path>
                                        <path d="M45.7649 37.8594H42.9961V40.6282H45.7649V37.8594Z" fill="#0078D4">
                                        </path>
                                        <path d="M42.6134 27.3624C42.5171 27.3143 42.3727 27.2661 42.2282 27.218C41.0244 26.8087 38.5445 26.2308 34.3311 26.4475C30.1177 26.6642 25.9766 27.5791 25.9766 27.5791C25.9766 27.5791 26.9155 33.1889 28.1675 36.0059C29.3954 38.7265 30.1418 40.436 31.2975 41.9046C33.344 44.6975 36.0887 46.2384 39.4835 46.2384C44.8285 46.2384 49.1863 41.9047 49.1863 36.5356C49.2104 32.2981 46.4657 28.6626 42.6134 27.3624ZM39.5316 45.0105C34.8608 45.0105 31.0808 41.2305 31.0808 36.5837C31.0808 31.9129 34.8608 28.1329 39.5316 28.1329C44.2025 28.1329 47.9825 31.9129 47.9825 36.5837C47.9584 41.2305 44.1784 45.0105 39.5316 45.0105Z"
                                              fill="#0078D4">
                                        </path>
                                        <path d="M40.3235 18.624L35.7249 13.9531H16.8008V42.4116H29.9706C29.754 42.0504 29.4891 41.6171 29.3687 41.3281C29.2965 41.1837 29.2243 41.0151 29.1761 40.8707H18.2694V15.3736H34.8823V19.3222H38.9512V25.2691L40.3235 25.4858V18.624Z"
                                              fill="#0078D4">
                                        </path>
                                        <path d="M35.6544 23.8476H22.1474C21.7622 23.8476 21.4492 23.5346 21.4492 23.1494C21.4492 22.7642 21.7622 22.4512 22.1474 22.4512H35.6544C36.0396 22.4512 36.3526 22.7642 36.3526 23.1494C36.3526 23.5346 36.0396 23.8476 35.6544 23.8476Z"
                                              fill="#0078D4">
                                        </path>
                                        <path d="M22.1474 36.0768C21.7622 36.0768 21.4492 36.3898 21.4492 36.775C21.4492 37.1603 21.7622 37.4732 22.1474 37.4732H27.372L26.7942 36.0527H22.1474V36.0768Z"
                                              fill="#0078D4">
                                        </path>
                                        <path d="M22.1474 31.4792C21.7622 31.4792 21.4492 31.7921 21.4492 32.1774C21.4492 32.5626 21.7622 32.8756 22.1474 32.8756H25.8071L25.4459 31.4551H22.1474V31.4792Z"
                                              fill="#0078D4">
                                        </path>
                                        <path d="M22.1474 26.9284C21.7622 26.9284 21.4492 27.2414 21.4492 27.6266C21.4492 28.0118 21.7622 28.3248 22.1474 28.3248H24.8199L24.6755 26.9043H22.1474V26.9284Z"
                                              fill="#0078D4">
                                        </path>
                                    </svg>
                                </div>
                                <h2>
                                    Azure AI Document Intelligence
                                </h2>
                                <h4>
                                    The AI-powered document extraction service that understands your forms
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <div>
                            <ul class="sub_list">
                                <li style="padding-left: 20px;">
                                    <h2>
                                        The AI-powered document extraction service that understands your forms
                                    </h2>
                                </li>
                                <li style="padding-right: 20px;">
                                    Azure AI Document Intelligence learns the structure of your forms to intelligently extract text and data. It ingests text from forms,
                                    applies machine learning technology to identify keys and tables, and then outputs structured data that includes the relationships
                                    within the original file. That way, you can extract information quickly, accurately, and tailored to your specific content, without
                                    heavy manual intervention or extensive data science expertise.
                                </li>
                            </ul>
                        </div>
                        <div class="pricing-page-section">
                            <h2>
                                Pricing details
                            </h2>
                        </div>
                        <!-- BEGIN: TAB-CONTROL -->
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                            <div class="tab-container-container">
                                <div class="tab-container-box">
                                    <div class="tab-container">
                                        <div class="dropdown-container software-kind-container" style="display:none;">
                                            <label>
                                                OS/Software:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               Azure AI Document Intelligence
              </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#tabContent1" href="javascript:void(0)" id="Azure AI Document Intelligence">
                                                            Azure AI Document Intelligence
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                                <option data-href="#tabContent1" selected="selected" value="Azure AI Document Intelligence">
                                                    Azure AI Document Intelligence
                                                </option>
                                            </select>
                                        </div>
                                        <div class="dropdown-container region-container">
                                            <label>
                                                Region:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               China East 2
              </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                                            China North 3
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                                                            China East 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                                                            China North 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                                                            China East
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                                                            China North
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                                <option data-href="#north-china3" selected="selected" value="north-china3">
                                                    China North 3
                                                </option>
                                                <option data-href="#east-china2" value="east-china2">
                                                    China East 2
                                                </option>
                                                <option data-href="#north-china2" value="north-china2">
                                                    China North 2
                                                </option>
                                                <option data-href="#east-china" value="east-china">
                                                    China East
                                                </option>
                                                <option data-href="#north-china" value="north-china">
                                                    China North
                                                </option>
                                            </select>
                                        </div>
                                        <div class="clearfix">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- BEGIN: TAB-CONTAINER-1 -->
                            <div class="tab-content">
                                <!-- BEGIN: TAB-CONTENT-1 -->
                                <div class="tab-panel" id="tabContent1">
                                    <!-- BEGIN: Tab level 2 navigator 2 -->
                                    <!-- BEGIN: Tab level 2 content 3 -->
                                    <ul class="tab-nav" style="display:none">
                                        <li class="active">
                                            <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="gpv1">
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content">
                                        <!-- BEGIN: Table1-Content-->
                                        <div class="tab-panel" id="tabContent2">
                                            <div>
                                                <div>
                                                    <p>
                                                        Azure AI Document Intelligence extracts key value pairs and tables from documents and includes the following
                                                        options:
                                                    </p>
                                                    <ul>
                                                        <li>
                                                            <strong>
                                                                Custom
                                                            </strong>
                                                            – Azure AI Document Intelligence learns the structure of your forms (invoices, Pos, industry specific records)
                                                            to intelligently extract text and data. It ingests text from forms, and then outputs structured data that
                                                            includes the relationships within the original file. That way, you can extract information quickly,
                                                            accurately, and tailored to your specific content, without heavy manual intervention or extensive data science
                                                            expertise.
                                                        </li>
                                                        <li>
                                                            <strong>
                                                                Prebuilt
                                                            </strong>
                                                            – Azure AI Document Intelligence supports various Pre-built models for special document types. Detects and
                                                            extracts data from US receipts and English Business cards using optical character recognition (OCR). Our
                                                            Pre-built models enable you to easily extract structured data from receipts and business cards.
                                                        </li>
                                                        <li>
                                                            <strong>
                                                                Layout
                                                            </strong>
                                                            – Detects and extracts text and table structure (row1, column1…row n column n) from documents using optical
                                                            character recognition (OCR).
                                                        </li>
                                                    </ul>
                                                </div>
                                                <br/>
                                                <table cellpadding="0" cellspacing="0" id="API_Azure_Form_Recognizer" width="100%">
                                                    <tr>
                                                        <th align="left">
                                                            <strong>
                                                                Instance
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                Document type
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                Price
                                                            </strong>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Free - Web/Container
                                                        </td>
                                                        <td>
                                                        </td>
                                                        <td>
                                                            0 - 500 pages free per month
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td rowspan="10">S0 - Web/Container</td>
                                                    </tr>
                                                    <tr>
                                                        <!-- <td>
                                                        </td> -->
                                                        <td>
                                                            Custom classification
                                                        </td>
                                                        <td>
                                                            ￥30.52 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Custom extraction
                                                        </td>
                                                        <td>
                                                            ￥305.28 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Pre-built: Layout, Receipt, Business Card, ID
                                                        </td>
                                                        <td>
                                                            ￥101.76 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Read
                                                        </td>
                                                        <td>
                                                            0-1000 transactions - ￥15.264 per 1,000 transactions
                                                            <br/>
                                                            1000+ transactions - ￥6.1056 per 1,000 transactions
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <!-- <td>
                                                         S0 - Web/容器
                                                        </td> -->
                                                        <td>
                                                        Custom generative extraction
                                                        </td>
                                                        <td>
                                                        ￥305.28 per 1,000 pages
                                                        </td>
                                                       </tr>
                                                      <tr>
                                                        <!-- <td>
                                                         S0 - Web/容器
                                                        </td> -->
                                                        <td>
                                                        Training
                                                        </td>
                                                        <td>
                                                        ￥30.528 per hour of training
                                                        </td>
                                                       </tr>
                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Batch custom extraction pages
                                                        </td>
                                                        <td>
                                                            ￥305.28 per 1,000 pages
                                                        </td>
                                                    </tr>


                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Batch custom generative extraction pages
                                                        </td>
                                                        <td>
                                                            ￥305.28 per 1,000 pages
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Batch custom classification
                                                        </td>
                                                        <td>
                                                            ￥30.528 per 1,000 pages
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Batch prebuilt models
                                                        </td>
                                                        <td>
                                                            ￥101.76 per 1,000 pages
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <!-- <td>
                                                            S0 - Web/Container
                                                        </td> -->
                                                        <td>
                                                            Batch read
                                                        </td>
                                                        <td>
                                                            ￥15.264 per 1,000 pages
                                                        </td>
                                                    </tr>

                                                </table>
                                            </div>
                                            <div>
                                                Commitment Tiers: pay an upfront monthly fee for high-volume usage at a discount.
                                                <br/>
                                                <br/>
                                                <table cellpadding="0" cellspacing="0" id="API_Azure_Form_Recognizer-admit" width="100%">
                                                    <tr>
                                                        <th align="left">
                                                            <strong>
                                                                Instance
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                Features
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                Price(per month)
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                Overage
                                                            </strong>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td rowspan="2">
                                                            Azure
                                                        </td>
                                                        <td>
                                                            Custom
                                                        </td>
                                                        <td>
                                                            ￥ 5495.04 per 20,000 pages
                                                            <br/>
                                                            ￥24422.4 per 100,000 pages
                                                            <br/>
                                                            ￥ 106848 per 500,000 pages
                                                        </td>
                                                        <td>
                                                            ￥274.752 per 1,000 pages
                                                            <br/>
                                                            ￥244.224 per 1,000 pages
                                                            <br/>
                                                            ￥213.696 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Prebuilt
                                                        </td>
                                                        <td>
                                                            ￥1933.44 per 20,000 pages
                                                            <br/>
                                                            ￥9158.4 per 100,000 pages
                                                            <br/>
                                                            ￥40704 per 500,000 pages
                                                        </td>
                                                        <td>
                                                            ￥96.672 per 1,000 pages
                                                            <br/>
                                                            ￥91.584 per 1,000 pages
                                                            <br/>
                                                            ￥81.408 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td rowspan="2">
                                                            Connected Container
                                                        </td>
                                                        <td>
                                                            Custom
                                                        </td>
                                                        <td>
                                                            ￥4670.784 per 20,000 pages
                                                            <br/>
                                                            ￥20759.04 per 100,000 pages
                                                            <br/>
                                                            ￥90820.8 per 500,000 pages
                                                        </td>
                                                        <td>
                                                            ￥233.539 per 1,000 pages
                                                            <br/>
                                                            ￥207.59 per 1,000 pages
                                                            <br/>
                                                            ￥181.64 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Prebuilt
                                                        </td>
                                                        <td>
                                                            ￥1546.752 per 20,000 pages
                                                            <br/>
                                                            ￥7326.72 per 100,000 pages
                                                            <br/>
                                                            ￥32563.2 per 500,000 pages
                                                        </td>
                                                        <td>
                                                            ￥77.3376 per 1,000 pages
                                                            <br/>
                                                            ￥73.2672 per 1,000 pages
                                                            <br/>
                                                            ￥65.1264 per 1,000 pages
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td rowspan="2">
                                                            Disconnected container
                                                        </td>
                                                        <td>
                                                            Custom
                                                        </td>
                                                        <td colspan="2">
                                                            ￥56049.4 per 20,000 pages per year
                                                            <br>
                                                            ￥249108.48 per 100,000 pages per year
                                                            <br/>
                                                            ￥1089849.6 per 500,000 pages per year
                                                            <br>
                                                            ￥1868313.6 per 1,000,000 pages per year
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Prebuilt
                                                        </td>
                                                        <td colspan="2">
                                                            ￥87920.64 per 100,000 pages per year
                                                            <br/>
                                                            ￥390758.4 per 500,000 pages per year
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                        <!-- END: Table1-Content-->
                                    </div>
                                </div>
                                <!-- END: TAB-CONTENT-3 -->
                            </div>
                            <!-- END: TAB-CONTAINER-1 -->
                        </div>
                        <!-- END: TAB-CONTROL -->
                        <!-- 修改常见问题 -->
                        <!-- <div class="pricing-page-section">
                                                <div class="more-detail">
                                                    <h2>FAQ</h2>
                                                    <em>Expand all</em>
                                                    <ul>
                                                        <li>
                                                            <i class="icon icon-plus"></i>
                                                            <div>
                                                                <a id="Storage_question14">How will partial hours be charged?</a>
                                                                <section>
                                                                    <p>Partial Hours will be charged as full hours.</p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus"></i>
                                                            <div>
                                                                <a id="Storage_question15">How will billing work when accessing private endpoint from peered network?</a>
                                                                <section>
                                                                    <p>While accessing Private Endpoints from peered Network, you will only be charged for Private Link Premium. You will not be charged for Peering.</p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus"></i>
                                                            <div>
                                                                <a id="Storage_question16">Will I be charged for Data transfer in addition to Private Link?</a>
                                                                <section>
                                                                    <p>Yes, above prices are premiums for Private Link capability. Data transfer <a href="../bandwidth/index.html">pricing</a> will apply to data.</p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div> -->
                        <!-- 修改服务级别协议 -->
                        <!-- <div class="pricing-page-section">
                                                <h2>Support</h2>
                                                <ul>
                                                    <li>If you have any questions or need help, please visit <a href="https://support.azure.cn/en-us/support/contact" id="sql-server-stretch-contact-page">Azure Support</a> and select self-help service or any other method to contact us for support.</li>
                                                    <li>We guarantee that Azure Private Link will be available at least 99.99% of the time. To learn more, please visit the <a href="../../../support/sla/private-link/index.html">SLA</a> page.</li>
                                                </ul>
                                            </div> -->
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
    <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
    </script>
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token =
                '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
                .toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
    </script>
    <!-- <script src='../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'>
         </script> -->
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</div>
</body>
</html>
